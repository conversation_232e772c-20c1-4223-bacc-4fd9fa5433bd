import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';

const MusicCreationDesign: React.FC = () => {
  const [content, setContent] = React.useState<string>('');

  React.useEffect(() => {
    const loadMarkdownContent = async () => {
      try {
        const response = await fetch(`${import.meta.env.VITE_PUBLIC_BASE_PATH}docs/MusicCreationDesign.md`);
        const text = await response.text();
        setContent(text);
      } catch (error) {
        console.error('Error loading markdown:', error);
      }
    };

    loadMarkdownContent();
  }, []);

  // 简单的 Markdown 渲染函数
  const renderMarkdown = (text: string) => {
    return text
      .split('\n')
      .map((line, index) => {
        if (line.startsWith('# ')) {
          return <h1 key={index} className="text-3xl font-bold text-gray-800 mb-6">{line.substring(2)}</h1>;
        }
        if (line.startsWith('## ')) {
          return <h2 key={index} className="text-2xl font-semibold text-gray-700 mb-4 mt-8">{line.substring(3)}</h2>;
        }
        if (line.startsWith('### ')) {
          return <h3 key={index} className="text-xl font-medium text-gray-600 mb-3 mt-6">{line.substring(4)}</h3>;
        }
        if (line.trim() === '') {
          return <br key={index} />;
        }
        return <p key={index} className="text-gray-600 mb-2 leading-relaxed">{line}</p>;
      });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center">
            <Link 
              to="/" 
              className="flex items-center text-gray-600 hover:text-gray-800 transition-colors mr-4"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              返回首页
            </Link>
            <h1 className="text-2xl font-bold text-gray-800">AI音乐创作页面设计说明</h1>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="bg-white rounded-lg shadow-sm p-8">
          {content ? (
            <div className="prose prose-lg max-w-none">
              {renderMarkdown(content)}
            </div>
          ) : (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-gray-500">加载中...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MusicCreationDesign;