# 二创页面功能设计文档 (优化版 V2.0)

# 待优化
- [ ]可配置参数调一下

## 🎯 设计背景

基于心弦 MoodBeat 平台的核心理念“让每个故事都能成为一首歌”，二创功能是连接用户情感共鸣与创作表达的重要桥梁。当用户在音乐欣赏页面点击“二创”按钮后，将进入一个专为移动端设计的二创工作空间，在这里用户可以基于原作品进行从一键“换肤”到精细化“重塑”的个性化再创作。

## 🎵 用户二创动机分析

### 1. 情感表达动机
- **个人化演绎**：用户希望用自己的方式重新诠释喜爱的音乐。
- **情感共鸣延伸**：在原作基础上融入自己的故事和情感。
- **技能展示**：展现自己的音乐创作能力和独特审美。

### 2. 社交互动动机
- **致敬原作**：向喜爱的创作者表达敬意。
- **社区参与**：参与平台的创作生态，获得认同感。
- **创作挑战**：接受不同风格的创作挑战，提升技能。

### 3. 商业价值动机
- **作品变现**：优秀的二创作品可能获得商业价值。
- **粉丝积累**：通过二创作品吸引关注，建立个人品牌。
- **学习成长**：在二创过程中学习和提升音乐创作技能。

## 🛠 二创页面核心功能设计

### 1. 智能二创引导系统 (AI小弦)

AI小弦在二创页面中扮演着核心引导者和创作伙伴的角色，其交互方式与主创作页面保持一致，通过顶部的对话气泡提供实时帮助。

- **上下文感知建议**：小弦会识别原作品的风格和情绪，并提出建议。“这首民谣很温暖，要不要试试把它变成一个更慵懒的Lofi版？”
- **目标导向辅助**：用户可以直接对小弦下达指令。“小弦，把主歌的吉他换成钢琴。”“帮我把副歌的歌词改得更有力量一些。”
- **技术参数解释**：当用户调整专业参数时，小弦会用通俗的语言解释其作用。“提高BPM会让歌曲听起来更有活力哦！”

### 2. 双模态二创工作台 (兼顾简易与专业)

为了满足不同用户的需求，二创工作台采用“上简下专”的纵向布局，用户既可以快速完成修改，也可以向下滚动进行深度定制。

#### **模式一：智能快捷二创 (页面上部)**

此区域提供高层级、效果显著的一键式修改选项，以卡片或大按钮形式呈现，满足快速、简单的改编需求。

- **风格转换 (Style Swap)**：提供“摇滚”、“爵士”、“古风”、“赛博朋克”等风格模板，一键应用，AI自动重构编曲。
- **情绪重塑 (Mood Remap)**：选择新的情绪，如“治愈”→“激昂”，“忧伤”→“希望”，AI会智能调整和弦、配器和节奏。
- **乐器主角 (Lead Instrument Change)**：快速更换主奏乐器，如“吉他版”、“钢琴版”、“弦乐版”、“合成器版”。
- **人声替换 (Vocal Swap)**：
    - **虚拟歌手**：选择不同的AI虚拟歌手音色。
    - **声音克隆**：一键应用用户自己的克隆声音进行演唱。

#### **模式二：模块化精细调校 (页面下部)**

此区域为可展开的模块化编辑器，允许用户对音乐的各个维度进行精细调整，满足专业和深度的改编需求。

- **🎵 音乐结构 (Music Structure)**
    - **速度 (BPM)**：通过滑块调整歌曲的整体速度。
    - **调性 (Key)**：一键升降调，改变歌曲的色彩。
    - **和声进行 (Harmony)**：提供常见和弦进行模板替换，或由AI推荐新的和声。

- **🎹 音轨元素 (Track Elements)**
    - 以简化的多轨道列表形式展示，如：人声、主旋律、贝斯、鼓、和声乐器等。
    - **单轨操作**：可对每个音轨进行“静音”、“替换音色”、“调整音量”等操作。
    - **AI智能分轨**：AI自动将原曲分离成不同轨道，方便用户编辑。

- **✍️ 歌词再创 (Lyrics Rework)**
    - **文本编辑器**：左侧显示原歌词，右侧为编辑区，方便对照修改。
    - **AI写词辅助**：选中某句歌词，可请求小弦“同义改写”、“优化韵脚”或“延伸意境”。
    - **保留/删除**：用户可自由选择保留、修改或完全重写部分歌词。

- **🎤 人声定制 (Vocal Customization)**
    - **声音特征调节**：调整演唱声音的“温柔/有力”、“低沉/明亮”等参数。
    - **效果器**：添加混响、合唱、延迟等常见人声效果。

### 3. 创作过程记录系统

- **版本管理**：自动保存每一次重大修改，用户可以随时回溯到任意历史版本，并进行对比试听。
- **创作故事生成**：基于用户的修改步骤（如“将风格从流行改为摇滚”、“重写了副歌歌词”），自动生成创作故事文案，方便发布时分享。

## 💰 原作者收益分配机制

### 1. 透明化收益展示
- **二创前预览**：开始二创前，清晰展示基于AI预估的改编程度和对应的分成比例。
- **发布时确认**：发布作品时，再次展示最终由系统评定的分成比例，供用户确认。
- **分成结构**：原作者 (30%-70%) + 二创者 (20%-60%) + 平台 (10%)。

### 2. 智能改编度评估
- **AI评估系统**：综合分析旋律相似度、和声结构、编曲创新、歌词原创度等多个维度，动态计算改编幅度。
- **人工审核机制**：为高收益或有争议的作品引入人工审核，确保公平性。创作者可发起申诉。

### 3. 收益实时追踪
- **收益仪表板**：为原作者和二创者提供独立的仪表板，实时查看二创作品带来的收益数据。
- **激励机制**：优秀的二创作品可获得平台额外奖励；被二创次数多的原作者可获得创作激励金。

## 🎨 页面交互设计 (Mobile-First)

### 1. 整体布局
- **顶部区域**：
    - **原作品信息**：固定显示“正在二创：《原作品名称》- @原作者”，保持上下文清晰。
    - **AI小弦对话区**：与主创作页一致，小弦以气泡形式提供引导和建议，是主要的交互入口。

- **核心创作区 (可滚动)**：
    - **上部**：醒目的“智能快捷二创”功能区，采用横向滑动的卡片样式。
    - **下部**：可折叠的“模块化精细调校”列表，每个模块（音乐结构、音轨元素等）是一个可点击展开的“手风琴”菜单。

- **二创成果展示**：
    - 把生成的音乐做成音乐卡片或列表，用户可以在这个区域查看所有二创作品并点击播放；
    - 这个列表里的项目用户可以随时删除；
    - 播放时，按钮周围会扩展出环形进度条，显示播放进度。

- **底部操作栏 (Bottom Action Bar)**：
    - **版本历史**：查看和恢复历史版本。
    - **重置**：一键撤销所有修改，恢复到原始状态。
    - **完成并发布**：进入最终发布流程。

### 2. 核心交互流程
1.  **进入页面**：用户点击“二创”，进入二创工作台。小弦出现并打招呼：“我们来给这首歌加点新花样吧！想先试试换个风格吗？”
2.  **快速尝试**：用户在“智能快捷二创”区域点击“摇滚”风格卡片。系统处理后，悬浮播放器自动播放摇滚版片段。
3.  **精细调整**：用户觉得贝斯声音太小，向下滚动，展开“音轨元素”模块，找到“贝斯”轨道，向右拖动音量滑块。
4.  **歌词修改**：用户展开“歌词再创”模块，选中一句歌词，请求小弦“帮我写得更酷一点”，AI提供几个选项供用户选择替换。
5.  **实时预览**：在整个过程中，用户可以随时点击悬浮播放器，试听最新的修改效果。
6.  **版本对比**：用户点击底部“版本历史”，在当前版本和“钢琴版”历史版本之间来回切换试听。
7.  **发布作品**：用户满意后，点击“完成并发布”，系统会展示改编度评估和收益分成，引导用户填写作品简介和创作故事，最终完成发布。

### 3. 社交互动功能
- **创作过程分享**：可将某个创作版本（如“我的摇滚版Demo”）分享到社区，提前获取反馈。
- **二创链展示**：在作品发布页，清晰展示从原作到当前作品的“二创链”，形成创作族谱。
- **@功能**：原作者和二创者可以在评论区互相@，进行交流和致敬。

## 🔄 二创生态循环

### 1. 多层级二创
- **二创的二创**：允许基于优秀的二创作品进行再次创作，并建立多层级的收益分配链条。
- **创作族谱**：可视化展示作品的创作传承关系。

### 2. 主题二创挑战
- **官方挑战**：平台定期发起围绕某首热门歌曲或某个主题的二创挑战赛。
- **奖励机制**：优秀的挑战作品可获得流量推广和现金奖励。

### 3. 学习成长体系
- **技能评估**：根据二创作品的复杂度和质量，评估用户的音乐技能。
- **个性化教程**：基于技能评估，小弦会推荐相应的编曲或作词教程。

## 📊 数据分析与优化

- **功能使用分析**：“快捷二创”与“精细调校”功能的使用比例，用以判断用户偏好。
- **改编度分布**：分析已发布二创作品的改编度分布，以优化AI评估模型的准确性。
- **转化率追踪**：监控从试听到点击二创，再到最终发布的完整转化漏斗。

## 🎯 成功指标

### 用户参与度
- **二创转化率**：从欣赏到二创的转化比例 > 15%
- **二创完成率**：开始二创到发布完成的比例 > 60%
- **高改编度作品比例**：改编度>40%的作品占总二创作品的比例 > 50%

### 作品质量
- **二创作品平均分享率** > 25%
- **被二次二创的作品比例** > 10%

### 生态健康度
- **原作者对二创生态的满意度** > 85%
- **收益分配争议率** < 5%
- **二创链条平均长度** > 2.5层

## 🚀 实施路线图

### 第一阶段（MVP）：核心快捷二创
- 实现“风格转换”和“人声替换”两个核心快捷功能。
- 基础的AI改编度评估和收益分配。
- 包含悬浮播放器和底部操作栏的基础交互框架。

### 第二阶段：精细化调校
- 上线“模块化精细调校”功能，首先支持音轨音量调整和BPM修改。
- 完善版本管理系统。

### 第三阶段：生态与社交
- 上线“二创的二创”功能。
- 推出“主题二创挑战”活动。
- 完善创作者个人主页的二创作品展示。