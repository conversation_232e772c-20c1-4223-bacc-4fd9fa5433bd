<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            color: white;
            font-family: Arial, sans-serif;
        }
        video {
            width: 100%;
            max-width: 800px;
            height: auto;
        }
        .info {
            margin: 20px 0;
            padding: 10px;
            background: #333;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>视频加载测试</h1>
    
    <div class="info">
        <h3>测试视频路径:</h3>
        <p id="video-path">/videos/MusicStory.mp4</p>
    </div>
    
    <video 
        id="test-video"
        controls 
        preload="metadata"
        poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 800 600'%3E%3Cdefs%3E%3ClinearGradient id='bg' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23667eea'/%3E%3Cstop offset='100%25' style='stop-color:%23764ba2'/%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23bg)'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='0.3em' fill='white' font-size='24' font-family='Arial'%3E🎵 视频加载中...%3C/text%3E%3C/svg%3E"
    >
        <source src="/videos/MusicStory.mp4" type="video/mp4">
        您的浏览器不支持视频播放。
    </video>
    
    <div class="info">
        <h3>加载状态:</h3>
        <p id="status">等待加载...</p>
    </div>
    
    <div class="info">
        <h3>错误信息:</h3>
        <p id="error">无</p>
    </div>
    
    <script>
        const video = document.getElementById('test-video');
        const statusEl = document.getElementById('status');
        const errorEl = document.getElementById('error');
        
        video.addEventListener('loadstart', () => {
            statusEl.textContent = '开始加载视频...';
        });
        
        video.addEventListener('loadedmetadata', () => {
            statusEl.textContent = '视频元数据加载完成';
        });
        
        video.addEventListener('loadeddata', () => {
            statusEl.textContent = '视频数据加载完成';
        });
        
        video.addEventListener('canplay', () => {
            statusEl.textContent = '视频可以播放';
        });
        
        video.addEventListener('canplaythrough', () => {
            statusEl.textContent = '视频完全加载，可以流畅播放';
        });
        
        video.addEventListener('error', (e) => {
            statusEl.textContent = '视频加载失败';
            errorEl.textContent = `错误: ${video.error ? video.error.message : '未知错误'}`;
            console.error('视频错误:', e, video.error);
        });
        
        // 检查文件是否存在
        fetch('/videos/MusicStory.mp4', { method: 'HEAD' })
            .then(response => {
                if (response.ok) {
                    console.log('视频文件存在，状态码:', response.status);
                } else {
                    console.error('视频文件不存在，状态码:', response.status);
                    errorEl.textContent = `文件不存在，HTTP状态码: ${response.status}`;
                }
            })
            .catch(err => {
                console.error('检查文件时出错:', err);
                errorEl.textContent = `网络错误: ${err.message}`;
            });
    </script>
</body>
</html>