import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { 
  Music, Sparkles, Play, Heart, Share2, ArrowLeft,
  Volume2, Pause, Send, User, Camera, Mic,
  X, ChevronDown, ChevronUp, Loader2, MoreVertical,
  PenTool, Zap, Clock, Gift, HeartCrack, Moon,
  Coffee, Sun, Star, Layers, Shuffle, RotateCcw,
  Check, AlertCircle, Headphones, SlidersHorizontal,
  FileText, Image, Hash, TrendingUp, Copy,
  Plus, Minus, ChevronRight, Bell, Link, Upload,
  Piano, Guitar, Drum, Radio, Settings, Palette,
  Brain, Wand2, History, DollarSign, Users,
  GitBranch, Award, Lightbulb, Save, RefreshCw,
  BarChart3, Gauge, Infinity, Sparkle, AudioLines
} from 'lucide-react';

// ==================== 类型定义 ====================
interface Particle {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  opacity: number;
  color: string;
  type: 'note' | 'star' | 'heart' | 'wave' | 'remix';
  rotation: number;
  rotationSpeed: number;
  pulse?: boolean;
}

interface MoodColor {
  primary: string;
  secondary: string;
  particles: string[];
  glow: string;
  background: string;
  accent: string;
}

interface OriginalTrack {
  id: string;
  title: string;
  artist: string;
  duration: string;
  coverUrl?: string;
  mood: string;
  style: string[];
  bpm: number;
  key: string;
  waveform: number[];
}

interface RemixStyle {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  description: string;
  color: string;
  intensity: number;
  preview?: string;
}

interface TrackElement {
  id: string;
  name: string;
  type: 'vocal' | 'melody' | 'bass' | 'drums' | 'harmony';
  volume: number;
  muted: boolean;
  replaced: boolean;
  instrument?: string;
  icon: React.ComponentType<any>;
}

interface RemixVersion {
  id: string;
  timestamp: Date;
  changes: string[];
  snapshot: any;
  intensity: number;
}

interface AIMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  suggestions?: string[];
  action?: string;
}

interface RevenueSplit {
  original: number;
  remix: number;
  platform: number;
}

// ==================== 配色系统 ====================
const moodColors: Record<string, MoodColor> = {
  happy: {
    primary: 'from-yellow-400 via-amber-400 to-orange-400',
    secondary: 'from-amber-300 to-yellow-300',
    particles: ['#FCD34D', '#FB923C', '#FBBF24', '#F59E0B'],
    glow: 'shadow-yellow-400/50',
    background: 'from-amber-50 to-orange-50',
    accent: '#F59E0B'
  },
  calm: {
    primary: 'from-blue-400 via-cyan-400 to-teal-400',
    secondary: 'from-teal-300 to-blue-300',
    particles: ['#60A5FA', '#06B6D4', '#14B8A6', '#10B981'],
    glow: 'shadow-cyan-400/50',
    background: 'from-blue-50 to-cyan-50',
    accent: '#06B6D4'
  },
  excited: {
    primary: 'from-purple-400 via-pink-400 to-rose-400',
    secondary: 'from-rose-300 to-purple-300',
    particles: ['#C084FC', '#F472B6', '#EC4899', '#DB2777'],
    glow: 'shadow-pink-400/50',
    background: 'from-purple-50 to-pink-50',
    accent: '#EC4899'
  },
  dark: {
    primary: 'from-gray-700 via-gray-800 to-gray-900',
    secondary: 'from-gray-600 to-gray-700',
    particles: ['#4B5563', '#374151', '#1F2937', '#111827'],
    glow: 'shadow-gray-700/50',
    background: 'from-gray-800 to-gray-900',
    accent: '#6B7280'
  },
  energetic: {
    primary: 'from-red-500 via-orange-500 to-yellow-500',
    secondary: 'from-orange-400 to-red-400',
    particles: ['#EF4444', '#F97316', '#F59E0B', '#EAB308'],
    glow: 'shadow-orange-500/50',
    background: 'from-red-50 to-orange-50',
    accent: '#F97316'
  }
};

// ==================== 二创风格预设 ====================
const remixStyles: RemixStyle[] = [
  {
    id: 'lofi',
    name: 'Lo-Fi',
    icon: Coffee,
    description: '慵懒温暖的卧室音乐',
    color: 'from-amber-400 to-orange-400',
    intensity: 30
  },
  {
    id: 'rock',
    name: '摇滚',
    icon: Zap,
    description: '激烈有力的电吉他',
    color: 'from-red-500 to-orange-500',
    intensity: 70
  },
  {
    id: 'jazz',
    name: '爵士',
    icon: Music,
    description: '优雅慵懒的萨克斯',
    color: 'from-purple-500 to-indigo-500',
    intensity: 45
  },
  {
    id: 'electronic',
    name: '电子',
    icon: Radio,
    description: '动感十足的合成器',
    color: 'from-cyan-500 to-blue-500',
    intensity: 65
  },
  {
    id: 'acoustic',
    name: '原声',
    icon: Guitar,
    description: '纯净自然的木吉他',
    color: 'from-green-500 to-teal-500',
    intensity: 25
  },
  {
    id: 'orchestral',
    name: '交响',
    icon: Piano,
    description: '恢弘大气的管弦乐',
    color: 'from-indigo-500 to-purple-500',
    intensity: 80
  }
];

// ==================== 主组件 ====================
const RemixCreationPage: React.FC = () => {
  // ========== 状态管理 ==========
  const [currentMood, setCurrentMood] = useState<string>('excited');
  const [particles, setParticles] = useState<Particle[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());
  
  // 原始作品信息
  const [originalTrack] = useState<OriginalTrack>({
    id: 'original-001',
    title: '夏日微风',
    artist: '@音乐创作者',
    duration: '3:24',
    mood: 'happy',
    style: ['流行', '民谣'],
    bpm: 120,
    key: 'C大调',
    waveform: Array.from({ length: 50 }, () => Math.random() * 100)
  });

  // 二创状态
  const [selectedStyle, setSelectedStyle] = useState<RemixStyle | null>(null);
  const [remixIntensity, setRemixIntensity] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  
  // 音轨控制
  const [trackElements, setTrackElements] = useState<TrackElement[]>([
    { id: 'vocal', name: '人声', type: 'vocal', volume: 80, muted: false, replaced: false, icon: Mic },
    { id: 'melody', name: '主旋律', type: 'melody', volume: 70, muted: false, replaced: false, icon: Music },
    { id: 'bass', name: '贝斯', type: 'bass', volume: 60, muted: false, replaced: false, icon: Radio },
    { id: 'drums', name: '鼓组', type: 'drums', volume: 75, muted: false, replaced: false, icon: Drum },
    { id: 'harmony', name: '和声', type: 'harmony', volume: 50, muted: false, replaced: false, icon: Piano }
  ]);

  // 版本管理
  const [versions, setVersions] = useState<RemixVersion[]>([]);
  const [currentVersion, setCurrentVersion] = useState<RemixVersion | null>(null);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [playingVersionId, setPlayingVersionId] = useState<string | null>(null);
  
  // 人声设置模态框
  const [showVocalModal, setShowVocalModal] = useState(false);
  const [vocalSettings, setVocalSettings] = useState({
    voiceType: 'female',
    gentleness: 50, // 温柔度 0-100
    brightness: 50  // 明亮度 0-100
  });

  // 收益分配
  const [revenueSplit, setRevenueSplit] = useState<RevenueSplit>({
    original: 50,
    remix: 40,
    platform: 10
  });

  // AI小弦
  const [aiMessage, setAiMessage] = useState('');
  const [aiTypingMessage, setAiTypingMessage] = useState('');
  const [showAiChat, setShowAiChat] = useState(false);
  const [aiMessages, setAiMessages] = useState<AIMessage[]>([]);
  
  // 高级参数
  const [showAdvancedParams, setShowAdvancedParams] = useState({
    structure: false,
    tracks: false,
    lyrics: false,
    effects: false
  });

  const [musicParams, setMusicParams] = useState({
    bpm: 120,
    key: 'C',
    timeSignature: '4/4',
    energy: 50,
    mood: 'happy'
  });

  const [lyricsContent, setLyricsContent] = useState('');
  
  // 播放控制
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackProgress, setPlaybackProgress] = useState(0);
  
  // Refs
  const animationRef = useRef<number | undefined>(undefined);
  const typingTimer = useRef<NodeJS.Timeout | null>(null);

  // ========== 生命周期 ==========
  useEffect(() => {
    // 初始化粒子系统
    initParticles();
    
    // 初始化AI消息
    setAiMessage('我们来给《夏日微风》加点新花样吧！试试下面的风格转换？');
    
    // 时间更新
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    
    return () => clearInterval(timer);
  }, []);

  // 打字机效果
  useEffect(() => {
    if (aiMessage) {
      setAiTypingMessage('');
      let index = 0;
      
      const typeChar = () => {
        if (index < aiMessage.length) {
          setAiTypingMessage(aiMessage.substring(0, index + 1));
          index++;
          typingTimer.current = setTimeout(typeChar, 30);
        }
      };
      
      typingTimer.current = setTimeout(typeChar, 300);
      
      return () => {
        if (typingTimer.current) clearTimeout(typingTimer.current);
      };
    }
  }, [aiMessage]);

  // ========== 粒子系统 ==========
  const initParticles = useCallback(() => {
    const newParticles: Particle[] = [];
    const colors = moodColors[currentMood].particles;
    
    for (let i = 0; i < 20; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * 430,
        y: Math.random() * 932,
        vx: (Math.random() - 0.5) * 0.3,
        vy: (Math.random() - 0.5) * 0.3,
        size: Math.random() * 2 + 0.5,
        opacity: Math.random() * 0.3 + 0.1,
        color: colors[Math.floor(Math.random() * colors.length)],
        type: Math.random() > 0.7 ? 'remix' : 'note',
        rotation: Math.random() * 360,
        rotationSpeed: (Math.random() - 0.5) * 2,
        pulse: Math.random() > 0.8
      });
    }
    setParticles(newParticles);
  }, [currentMood]);

  useEffect(() => {
    initParticles();
  }, [currentMood, initParticles]);

  // 粒子动画
  useEffect(() => {
    const animate = () => {
      setParticles(prevParticles => 
        prevParticles.map(particle => {
          const newX = particle.x + particle.vx;
          const newY = particle.y + particle.vy;
          let newVx = particle.vx;
          let newVy = particle.vy;
          
          if (newX <= 0 || newX >= 430) newVx = -newVx;
          if (newY <= 0 || newY >= 932) newVy = -newVy;
          
          const pulseOpacity = particle.pulse 
            ? particle.opacity + Math.sin(Date.now() * 0.003) * 0.1
            : particle.opacity;
          
          return {
            ...particle,
            x: newX,
            y: newY,
            vx: newVx,
            vy: newVy,
            rotation: particle.rotation + particle.rotationSpeed,
            opacity: Math.max(0.1, Math.min(0.4, pulseOpacity))
          };
        })
      );
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);
    return () => {
      if (animationRef.current) cancelAnimationFrame(animationRef.current);
    };
  }, []);

  // ========== 风格选择处理 ==========
  const handleStyleSelect = useCallback((style: RemixStyle) => {
    setSelectedStyle(style);
    setRemixIntensity(style.intensity);
    
    // 更新AI提示
    setAiMessage(`已选择${style.name}风格！${style.description}。要不要调整一下强度？`);
    
    // 更新心情颜色
    if (style.id === 'lofi') setCurrentMood('calm');
    else if (style.id === 'rock') setCurrentMood('energetic');
    else if (style.id === 'jazz') setCurrentMood('dark');
    
    // 保存版本
    saveVersion([`应用${style.name}风格`]);
  }, []);

  // ========== 音轨控制 ==========
  const handleTrackVolumeChange = useCallback((trackId: string, volume: number) => {
    setTrackElements(prev => prev.map(track => 
      track.id === trackId ? { ...track, volume } : track
    ));
  }, []);

  const handleTrackMute = useCallback((trackId: string) => {
    setTrackElements(prev => prev.map(track => 
      track.id === trackId ? { ...track, muted: !track.muted } : track
    ));
    
    const track = trackElements.find(t => t.id === trackId);
    if (track) {
      setAiMessage(`${track.muted ? '打开' : '静音'}了${track.name}轨道`);
    }
  }, [trackElements]);

  const handleTrackReplace = useCallback((trackId: string) => {
    const track = trackElements.find(t => t.id === trackId);
    if (track) {
      if (trackId === 'vocal') {
        // 人声音轨打开设置模态框
        setShowVocalModal(true);
        setAiMessage(`让我们重新设置人声参数吧！`);
      } else {
        setAiMessage(`正在为${track.name}选择新的音色...`);
      }
    }
  }, [trackElements]);

  // ========== 版本管理 ==========
  const saveVersion = useCallback((changes: string[]) => {
    const newVersion: RemixVersion = {
      id: `v${versions.length + 1}`,
      timestamp: new Date(),
      changes,
      snapshot: {
        style: selectedStyle,
        tracks: [...trackElements],
        params: { ...musicParams },
        intensity: remixIntensity
      },
      intensity: remixIntensity
    };
    
    setVersions(prev => [...prev, newVersion]);
    setCurrentVersion(newVersion);
  }, [versions, selectedStyle, trackElements, musicParams, remixIntensity]);

  const restoreVersion = useCallback((version: RemixVersion) => {
    setSelectedStyle(version.snapshot.style);
    setTrackElements(version.snapshot.tracks);
    setMusicParams(version.snapshot.params);
    setRemixIntensity(version.intensity);
    setCurrentVersion(version);
    setShowVersionHistory(false);
    
    setAiMessage(`已恢复到版本 ${version.id}`);
  }, []);

  // ========== 生成处理 ==========
  const handleGenerate = useCallback(async () => {
    if (!selectedStyle && remixIntensity === 0) {
      setAiMessage('先选择一个风格或调整一些参数吧！');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);
    
    // 模拟生成过程
    const steps = [
      { progress: 20, message: '分析原曲结构...' },
      { progress: 40, message: '应用风格转换...' },
      { progress: 60, message: '重新编排音轨...' },
      { progress: 80, message: '优化混音效果...' },
      { progress: 100, message: '二创完成！' }
    ];

    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, 600));
      setGenerationProgress(step.progress);
      setAiMessage(step.message);
    }

    setIsGenerating(false);
    setAiMessage('你的二创作品已经准备好了！可以试听或继续调整');
    
    // 更新收益分配
    updateRevenueSplit();
  }, [selectedStyle, remixIntensity]);

  // ========== 收益分配计算 ==========
  const updateRevenueSplit = useCallback(() => {
    // 根据改编强度计算分成
    const originalShare = Math.max(30, 70 - remixIntensity * 0.5);
    const remixShare = Math.min(60, 20 + remixIntensity * 0.5);
    
    setRevenueSplit({
      original: originalShare,
      remix: remixShare,
      platform: 10
    });
  }, [remixIntensity]);

  useEffect(() => {
    updateRevenueSplit();
  }, [remixIntensity, updateRevenueSplit]);

  // ========== AI聊天处理 ==========
  const handleAiChat = useCallback(() => {
    setShowAiChat(!showAiChat);
    if (!showAiChat) {
      setAiMessage('有什么需要帮助的吗？我可以帮你优化二创效果 🎵');
    }
  }, [showAiChat]);

  // ========== 渲染粒子形状 ==========
  const renderParticleShape = (type: Particle['type']) => {
    switch (type) {
      case 'remix': return '⚡';
      case 'note': return '♪';
      case 'star': return '✦';
      case 'heart': return '♥';
      case 'wave': return '~';
      default: return '•';
    }
  };

  // ========== 版本历史组件 ==========
  const VersionHistory = () => (
    <div 
      className="absolute inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-end justify-center"
      onClick={() => setShowVersionHistory(false)}
    >
      <div 
        className="w-full bg-gray-900 rounded-t-3xl" 
        style={{ maxHeight: '85%' }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 拖动指示条 */}
        <div className="flex justify-center pt-3 pb-2">
          <div className="w-12 h-1.5 bg-white/20 rounded-full" />
        </div>
        
        {/* 标题栏 */}
        <div className="px-6 pb-4 border-b border-white/10">
          <div className="flex items-center">
            <h3 className="text-white font-semibold text-lg flex items-center gap-2">
              <Layers className="w-5 h-5" />
              版本管理
            </h3>
          </div>
        </div>
        
        {/* 原作品信息 */}
        <div className="px-6 py-4 border-b border-white/10">
          <div className="bg-white/5 backdrop-blur-lg rounded-xl p-3">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <Music className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1">
                <div className="text-white/60 text-xs mb-0.5">原作品</div>
                <div className="text-white font-medium text-sm">{originalTrack.title}</div>
                <div className="text-white/50 text-xs">{originalTrack.artist} · {originalTrack.duration}</div>
              </div>
              <button className="text-white/60 hover:text-white transition-all">
                <Play className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
        
        {/* 版本列表 */}
        <div className="px-6 py-4 overflow-y-auto" style={{ maxHeight: 'calc(85vh - 240px)' }}>
          <h4 className="text-white/60 text-xs mb-3">二创版本记录</h4>
          <div className="space-y-3">
            {versions.map((version, index) => (
              <div 
                key={version.id}
                className="bg-white/5 rounded-xl p-3 hover:bg-white/10 transition-all"
              >
                <div className="flex items-center justify-between mb-1">
                  <span className="text-white text-sm font-medium">
                    版本 {index + 1}
                  </span>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setPlayingVersionId(playingVersionId === version.id ? null : version.id);
                      }}
                      className="p-1.5 hover:bg-white/20 rounded-full transition-all"
                    >
                      {playingVersionId === version.id ? (
                        <Pause className="w-4 h-4 text-white" />
                      ) : (
                        <Play className="w-4 h-4 text-white" />
                      )}
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        restoreVersion(version);
                      }}
                      className="text-white/60 hover:text-white transition-all text-xs px-2 py-1 rounded-md hover:bg-white/10"
                    >
                      查看修改
                    </button>
                    <span className="text-white/40 text-xs">
                      {version.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                </div>
                <div className="text-white/60 text-xs mb-2">
                  {version.changes.join('、')}
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-xs text-white/40">改编强度</div>
                  <div className="flex-1 h-1 bg-white/10 rounded-full">
                    <div 
                      className="h-full bg-purple-400 rounded-full"
                      style={{ width: `${version.intensity}%` }}
                    />
                  </div>
                  <div className="text-xs text-white/40">{version.intensity}%</div>
                </div>
                {playingVersionId === version.id && (
                  <div className="mt-2 flex items-center gap-2 text-xs text-purple-400">
                    <AudioLines className="w-3 h-3 animate-pulse" />
                    <span>正在播放预览...</span>
                  </div>
                )}
              </div>
            ))}
            
            {versions.length === 0 && (
              <div className="text-center text-white/40 py-8">
                还没有保存的版本
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  // ==================== 主渲染 ====================
  return (
    <div className="relative mx-auto" style={{ maxWidth: '430px', height: '932px' }}>
      {/* 手机框架 */}
      <div className="relative w-full h-full bg-black rounded-[3rem] p-[3px] shadow-2xl">
        <div className="relative w-full h-full bg-gray-900 rounded-[2.8rem] overflow-hidden flex flex-col">
          
          {/* 状态栏 */}
          <div className="absolute top-0 left-0 right-0 z-50 px-8 pt-3 pb-1">
            <div className="flex justify-between items-center text-white text-sm">
              <div className="flex items-center gap-1">
                <span className="font-medium">
                  {currentTime.getHours().toString().padStart(2, '0')}:
                  {currentTime.getMinutes().toString().padStart(2, '0')}
                </span>
              </div>
              <div className="absolute left-1/2 transform -translate-x-1/2 w-24 h-7 bg-black rounded-full" />
              <div className="flex items-center gap-1">
                <Bell className="w-3.5 h-3.5 text-white/70" />
                <div className="w-6 h-3 border border-white/50 rounded-sm">
                  <div className="h-full bg-white rounded-sm" style={{ width: '85%' }} />
                </div>
              </div>
            </div>
          </div>

          {/* 动态背景 */}
          <div className={`absolute inset-0 bg-gradient-to-br ${moodColors[currentMood].primary} opacity-10 transition-all duration-1000`} />
          
          {/* 粒子系统 */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {particles.map((particle) => (
              <div
                key={particle.id}
                className="absolute transition-opacity duration-500"
                style={{
                  left: `${particle.x}px`,
                  top: `${particle.y}px`,
                  transform: `translate(-50%, -50%) rotate(${particle.rotation}deg)`,
                  fontSize: `${particle.size * 8}px`,
                  color: particle.color,
                  opacity: particle.opacity,
                  filter: `blur(${particle.pulse ? 0 : 0.5}px)`,
                  textShadow: `0 0 ${particle.size * 4}px ${particle.color}`
                }}
              >
                {renderParticleShape(particle.type)}
              </div>
            ))}
          </div>

          {/* 主容器 */}
          <div className="flex flex-col h-full pt-14 relative">
            {/* AI小弦语音区 */}
            <div className="px-6 pb-3 z-20">
              <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
                <div className="flex items-start gap-3">
                  <div className={`w-10 h-10 rounded-full bg-gradient-to-br ${moodColors[currentMood].primary} flex items-center justify-center flex-shrink-0 animate-pulse-slow`}>
                    <Sparkles className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-white font-medium text-sm">小弦</span>
                      <span className="text-xs text-white/40">AI二创助手</span>
                    </div>
                    <p className="text-white/80 text-sm leading-relaxed">
                      {aiTypingMessage}
                      {aiTypingMessage.length < aiMessage.length && (
                        <span className="inline-block w-1 h-4 ml-1 bg-white/60 animate-blink" />
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* 主内容区域 */}
            <div className="flex-1 overflow-y-auto px-6 pb-32 no-scrollbar relative min-h-0">
              {/* 一键风格转换 */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-white/80 text-sm font-medium mb-3 flex items-center gap-2">
                    <Palette className="w-4 h-4" />
                    一键风格转换
                  </h3>
                  <div className="grid grid-cols-3 gap-2">
                    {remixStyles.slice(0, 6).map(style => (
                      <button
                        key={style.id}
                        className={`relative p-3 rounded-xl transition-all ${
                          selectedStyle?.id === style.id
                            ? 'scale-105 ring-2 ring-white/50'
                            : 'hover:scale-105'
                        }`}
                        onClick={() => handleStyleSelect(style)}
                      >
                        <div className={`absolute inset-0 bg-gradient-to-br ${style.color} rounded-xl opacity-80`} />
                        <div className="relative flex flex-col items-center gap-1">
                          <style.icon className="w-5 h-5 text-white" />
                          <span className="text-xs text-white font-medium">{style.name}</span>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* 改编强度 */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="text-white/80 text-sm flex items-center gap-2">
                      <Gauge className="w-4 h-4" />
                      改编强度
                    </label>
                    <span className="text-white/60 text-xs">{remixIntensity}%</span>
                  </div>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={remixIntensity}
                    onChange={(e) => setRemixIntensity(Number(e.target.value))}
                    className="w-full h-2 rounded-full cursor-pointer"
                    style={{
                      background: `linear-gradient(to right, 
                        ${remixIntensity > 70 ? '#F97316' : remixIntensity > 40 ? '#3B82F6' : '#10B981'} 0%, 
                        ${remixIntensity > 70 ? '#EF4444' : remixIntensity > 40 ? '#8B5CF6' : '#3B82F6'} ${remixIntensity}%, 
                        rgba(255,255,255,0.1) ${remixIntensity}%, 
                        rgba(255,255,255,0.1) 100%)`
                    }}
                  />
                  <div className="flex justify-between text-xs text-white/40 mt-1">
                    <span>微调</span>
                    <span>适中</span>
                    <span>重塑</span>
                  </div>
                </div>

                {/* 专业模式内容 */}
                <div className="space-y-3">
                  {/* 音乐结构 */}
                  <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 overflow-hidden">
                    <button
                      className="w-full px-4 py-3 flex items-center justify-between text-white hover:bg-white/5 transition-all"
                      onClick={() => setShowAdvancedParams({
                        ...showAdvancedParams,
                        structure: !showAdvancedParams.structure
                      })}
                    >
                      <span className="flex items-center gap-2">
                        <SlidersHorizontal className="w-4 h-4" />
                        音乐结构
                      </span>
                      <ChevronDown className={`w-4 h-4 transition-transform ${
                        showAdvancedParams.structure ? 'rotate-180' : ''
                      }`} />
                    </button>
                    
                    {showAdvancedParams.structure && (
                      <div className="px-4 pb-4 space-y-4 border-t border-white/10">
                        <div className="pt-3 space-y-3">
                          {/* BPM */}
                          <div>
                            <label className="text-white/60 text-xs mb-2 block">
                              速度 BPM: {musicParams.bpm}
                            </label>
                            <input
                              type="range"
                              min="60"
                              max="200"
                              value={musicParams.bpm}
                              onChange={(e) => setMusicParams({
                                ...musicParams,
                                bpm: Number(e.target.value)
                              })}
                              className="w-full h-2 bg-white/10 rounded-full appearance-none"
                            />
                          </div>
                          
                          {/* 调性 */}
                          <div>
                            <label className="text-white/60 text-xs mb-2 block">调性</label>
                            <div className="grid grid-cols-4 gap-2">
                              {['C', 'D', 'E', 'F', 'G', 'A', 'B', 'Cm'].map(key => (
                                <button
                                  key={key}
                                  className={`py-1.5 rounded-lg text-xs transition-all ${
                                    musicParams.key === key
                                      ? 'bg-purple-500/30 text-white border border-purple-400'
                                      : 'bg-white/10 text-white/60 hover:bg-white/20'
                                  }`}
                                  onClick={() => setMusicParams({ ...musicParams, key })}
                                >
                                  {key}
                                </button>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 音轨控制 */}
                  <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 overflow-hidden">
                    <button
                      className="w-full px-4 py-3 flex items-center justify-between text-white hover:bg-white/5 transition-all"
                      onClick={() => setShowAdvancedParams({
                        ...showAdvancedParams,
                        tracks: !showAdvancedParams.tracks
                      })}
                    >
                      <span className="flex items-center gap-2">
                        <AudioLines className="w-4 h-4" />
                        音轨元素
                      </span>
                      <ChevronDown className={`w-4 h-4 transition-transform ${
                        showAdvancedParams.tracks ? 'rotate-180' : ''
                      }`} />
                    </button>
                    
                    {showAdvancedParams.tracks && (
                      <div className="px-4 pb-4 space-y-3 border-t border-white/10">
                        <div className="pt-3">
                          {trackElements.map(track => (
                            <div key={track.id} className="flex items-center gap-3 py-2">
                              <track.icon className={`w-4 h-4 ${track.muted ? 'text-white/30' : 'text-white/70'}`} />
                              <span className={`text-sm ${track.muted ? 'text-white/30' : 'text-white/70'} w-16`}>
                                {track.name}
                              </span>
                              <input
                                type="range"
                                min="0"
                                max="100"
                                value={track.volume}
                                onChange={(e) => handleTrackVolumeChange(track.id, Number(e.target.value))}
                                className="flex-1 h-1 bg-white/10 rounded-full appearance-none"
                                disabled={track.muted}
                              />
                              <button
                                className={`p-1.5 rounded-lg transition-all ${
                                  track.muted 
                                    ? 'bg-red-500/20 text-red-400' 
                                    : 'bg-white/10 text-white/60 hover:bg-white/20'
                                }`}
                                onClick={() => handleTrackMute(track.id)}
                              >
                                <Volume2 className="w-3 h-3" />
                              </button>
                              <button
                                className="p-1.5 bg-white/10 hover:bg-white/20 rounded-lg text-white/60 transition-all"
                                onClick={() => handleTrackReplace(track.id)}
                              >
                                <RefreshCw className="w-3 h-3" />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 歌词编辑 */}
                  <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 overflow-hidden">
                    <button
                      className="w-full px-4 py-3 flex items-center justify-between text-white hover:bg-white/5 transition-all"
                      onClick={() => setShowAdvancedParams({
                        ...showAdvancedParams,
                        lyrics: !showAdvancedParams.lyrics
                      })}
                    >
                      <span className="flex items-center gap-2">
                        <FileText className="w-4 h-4" />
                        歌词再创
                      </span>
                      <ChevronDown className={`w-4 h-4 transition-transform ${
                        showAdvancedParams.lyrics ? 'rotate-180' : ''
                      }`} />
                    </button>
                    
                    {showAdvancedParams.lyrics && (
                      <div className="px-4 pb-4 border-t border-white/10">
                        <div className="pt-3">
                          <div className="bg-white/5 rounded-xl p-3">
                            <textarea
                              placeholder="在这里编辑歌词..."
                              value={lyricsContent}
                              onChange={(e) => setLyricsContent(e.target.value)}
                              className="w-full h-32 bg-transparent text-white placeholder-white/40 outline-none resize-none text-sm"
                            />
                          </div>
                          <div className="mt-2 flex gap-2">
                            <button className="px-3 py-1.5 bg-white/10 hover:bg-white/20 rounded-lg text-white/80 text-xs transition-all">
                              原词填充
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* 收益分配预览 */}
                <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-white/80 text-sm flex items-center gap-2">
                      <DollarSign className="w-4 h-4" />
                      收益分配预览
                    </span>
                    <span className="text-white/40 text-xs">基于当前改编程度</span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white/60 text-xs">原作者</span>
                      <span className="text-white text-sm font-medium">{revenueSplit.original}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/60 text-xs">二创者(你)</span>
                      <span className="text-green-400 text-sm font-medium">{revenueSplit.remix}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/60 text-xs">平台</span>
                      <span className="text-white/40 text-sm">{revenueSplit.platform}%</span>
                    </div>
                  </div>
                </div>

                {/* 波形对比 */}
                <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
                  <h3 className="text-white/80 text-sm font-medium mb-3">波形对比</h3>
                  <div className="space-y-3">
                    <div>
                      <div className="text-white/40 text-xs mb-1">原作品</div>
                      <div className="flex items-end justify-around h-12 px-2">
                        {originalTrack.waveform.slice(0, 30).map((height, i) => (
                          <div
                            key={i}
                            className="w-1 bg-white/30 rounded-full"
                            style={{ height: `${height * 0.5}%` }}
                          />
                        ))}
                      </div>
                    </div>
                    <div>
                      <div className="text-white/40 text-xs mb-1">二创版本</div>
                      <div className="flex items-end justify-around h-12 px-2">
                        {originalTrack.waveform.slice(0, 30).map((height, i) => (
                          <div
                            key={i}
                            className="w-1 bg-purple-400/50 rounded-full"
                            style={{ 
                              height: `${(height * 0.5 * (1 + remixIntensity / 100))}%`,
                              animation: isGenerating ? `wave-pulse 1s ease-in-out ${i * 30}ms infinite` : 'none'
                            }}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 生成进度条 */}
          {isGenerating && (
            <div className="absolute bottom-32 left-0 right-0 px-6 z-30">
              <div className="bg-black/80 backdrop-blur-lg rounded-2xl p-4 border border-white/20">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white text-sm">正在生成...</span>
                  <span className="text-white/60 text-sm">{generationProgress}%</span>
                </div>
                <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full transition-all duration-300"
                    style={{ width: `${generationProgress}%` }}
                  />
                </div>
              </div>
            </div>
          )}

          {/* 底部固定操作栏 - 优化布局 */}
          <div className="absolute bottom-0 left-0 right-0 z-40 px-6 pb-8 pt-4 bg-gradient-to-t from-gray-900 via-gray-900/95 to-transparent">
            <div className="flex items-center gap-3">
              {/* 返回按钮 */}
              <button className="w-14 h-14 bg-white/10 hover:bg-white/15 backdrop-blur-lg rounded-full flex items-center justify-center transition-all hover:scale-105 active:scale-95">
                <ArrowLeft className="w-6 h-6 text-white" />
              </button>
              
              {/* 二创我的音乐 */}
              <button
                className="flex-1 h-14 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-white font-medium transition-all hover:shadow-xl hover:scale-105 active:scale-95 shadow-lg shadow-purple-500/30"
                onClick={handleGenerate}
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    <span>生成中...</span>
                  </div>
                ) : (
                  <span className="flex items-center justify-center gap-2">
                    <Sparkles className="w-5 h-5" />
                    二创我的音乐
                  </span>
                )}
              </button>
              
              {/* 版本按钮 */}
              <button 
                className="relative w-14 h-14 bg-white/10 hover:bg-white/15 backdrop-blur-lg rounded-full flex items-center justify-center transition-all hover:scale-105 active:scale-95"
                onClick={() => setShowVersionHistory(true)}
              >
                <Layers className="w-6 h-6 text-white" />
                {versions.length > 0 && (
                  <div className="absolute top-1 right-1 w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center">
                    <span className="text-[10px] text-white font-bold">{versions.length}</span>
                  </div>
                )}
              </button>
              
              {/* 音律珠 - AI助手 */}
              <button
                onClick={handleAiChat}
                className="relative group"
              >
                {/* 呼吸光环 */}
                <div className="absolute -inset-1 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 animate-pulse-slow" />
                
                {/* 音律珠主体 */}
                <div className="relative w-14 h-14 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 via-pink-500 to-orange-500 shadow-2xl transform transition-all hover:scale-110">
                  {/* 内部流动效果 */}
                  <div className="absolute inset-0">
                    {[...Array(3)].map((_, i) => (
                      <div
                        key={i}
                        className="absolute w-full h-full animate-flow"
                        style={{
                          background: `radial-gradient(circle at ${30 + i * 20}% ${30 + i * 20}%, rgba(255,255,255,0.3) 0%, transparent 50%)`,
                          animationDelay: `${i * 0.5}s`
                        }}
                      />
                    ))}
                  </div>
                  
                  {/* 中心符号 */}
                  <div className="relative w-full h-full flex items-center justify-center">
                    <Sparkles className="w-6 h-6 text-white" />
                    {/* 状态指示点 */}
                    <div className="absolute bottom-1.5 right-1.5 w-2 h-2 rounded-full bg-white animate-pulse" />
                  </div>
                </div>
                
                {/* 提示文字 */}
                <div className="absolute -top-8 right-0 bg-black/80 text-white text-xs px-2 py-1 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  AI助手
                </div>
              </button>
            </div>
          </div>

          {/* 版本历史弹窗 */}
          {showVersionHistory && <VersionHistory />}

          {/* 人声设置模态框 */}
          {showVocalModal && (
            <div 
              className="absolute inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-end animate-fade-in"
              onClick={() => setShowVocalModal(false)}
            >
              <div 
                className="w-full bg-gray-900 rounded-t-3xl animate-slide-up" 
                style={{ maxHeight: '75%' }}
                onClick={(e) => e.stopPropagation()}
              >
                {/* 拖动指示条 */}
                <div className="flex justify-center pt-3 pb-2">
                  <div className="w-12 h-1.5 bg-white/20 rounded-full" />
                </div>
                
                {/* 标题栏 */}
                <div className="px-6 pb-4 border-b border-white/10">
                  <h3 className="text-white font-semibold text-lg flex items-center gap-2">
                    <Mic className="w-5 h-5" />
                    人声设置
                  </h3>
                </div>
                
                {/* 设置内容 */}
                <div className="px-6 py-4 overflow-y-auto" style={{ maxHeight: 'calc(75vh - 120px)' }}>
                  <div className="space-y-6">
                    {/* 人声选择 */}
                    <div>
                      <label className="text-white/80 text-sm mb-3 block font-medium">人声类型</label>
                      <div className="grid grid-cols-4 gap-3">
                        {[
                          { id: 'male', label: '男声', icon: '🎤' },
                          { id: 'female', label: '女声', icon: '🎵' },
                          { id: 'child', label: '童声', icon: '🎶' },
                          { id: 'clone', label: '克隆', icon: '🎙️' }
                        ].map(voice => (
                          <button
                            key={voice.id}
                            className={`py-3 rounded-xl text-white text-sm transition-all flex flex-col items-center gap-2 ${
                              vocalSettings.voiceType === voice.id
                                ? 'bg-purple-500/30 border border-purple-400'
                                : 'bg-white/10 hover:bg-white/20 border border-white/10'
                            }`}
                            onClick={() => setVocalSettings({ ...vocalSettings, voiceType: voice.id })}
                          >
                            <span className="text-xl">{voice.icon}</span>
                            <span className="text-xs">{voice.label}</span>
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* 声音特征 */}
                    <div>
                      <label className="text-white/80 text-sm mb-4 block font-medium">声音特征</label>
                      <div className="space-y-4">
                        {/* 温柔度 */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-xs text-white/60">温柔度</span>
                            <span className="text-xs text-white/60">{vocalSettings.gentleness}%</span>
                          </div>
                          <div className="flex items-center gap-3">
                            <span className="text-xs text-white/40 w-12">温柔</span>
                            <input 
                              type="range" 
                              min="0" 
                              max="100" 
                              value={vocalSettings.gentleness}
                              onChange={(e) => setVocalSettings({ ...vocalSettings, gentleness: Number(e.target.value) })}
                              className="flex-1 h-2 bg-white/10 rounded-full appearance-none cursor-pointer"
                              style={{
                                background: `linear-gradient(to right, #8B5CF6 0%, #8B5CF6 ${vocalSettings.gentleness}%, rgba(255,255,255,0.1) ${vocalSettings.gentleness}%, rgba(255,255,255,0.1) 100%)`
                              }}
                            />
                            <span className="text-xs text-white/40 w-12 text-right">有力</span>
                          </div>
                        </div>
                        
                        {/* 明亮度 */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-xs text-white/60">明亮度</span>
                            <span className="text-xs text-white/60">{vocalSettings.brightness}%</span>
                          </div>
                          <div className="flex items-center gap-3">
                            <span className="text-xs text-white/40 w-12">低沉</span>
                            <input 
                              type="range" 
                              min="0" 
                              max="100" 
                              value={vocalSettings.brightness}
                              onChange={(e) => setVocalSettings({ ...vocalSettings, brightness: Number(e.target.value) })}
                              className="flex-1 h-2 bg-white/10 rounded-full appearance-none cursor-pointer"
                              style={{
                                background: `linear-gradient(to right, #06B6D4 0%, #06B6D4 ${vocalSettings.brightness}%, rgba(255,255,255,0.1) ${vocalSettings.brightness}%, rgba(255,255,255,0.1) 100%)`
                              }}
                            />
                            <span className="text-xs text-white/40 w-12 text-right">明亮</span>
                          </div>
                        </div>
                      </div>
                    </div>


                  </div>
                </div>
                
                {/* 底部操作栏 */}
                <div className="px-6 py-4 border-t border-white/10 bg-gray-900/50">
                  <div className="flex gap-3">
                    <button 
                      className="flex-1 py-3 bg-white/10 hover:bg-white/15 rounded-xl text-white/80 text-sm font-medium transition-all"
                      onClick={() => setShowVocalModal(false)}
                    >
                      取消
                    </button>
                    <button 
                      className="flex-1 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 rounded-xl text-white text-sm font-medium transition-all"
                      onClick={() => {
                        setShowVocalModal(false);
                        setAiMessage('人声设置已更新！新的音色会让作品更有魅力 ✨');
                        // 这里可以添加保存设置的逻辑
                      }}
                    >
                      应用设置
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Home Indicator */}
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white/30 rounded-full" />
        </div>
      </div>
    </div>
  );
};

// ==================== 样式注入 ====================
const style = document.createElement('style');
style.textContent = `
  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes scale-up {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }
  
  @keyframes pulse-slow {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.1); }
  }
  
  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }
  
  @keyframes wave-pulse {
    0%, 100% { transform: scaleY(1); opacity: 0.5; }
    50% { transform: scaleY(1.5); opacity: 1; }
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  @keyframes flow {
    0% { transform: translateY(100%) rotate(0deg); }
    100% { transform: translateY(-100%) rotate(360deg); }
  }
  
  @keyframes slide-up {
    from { transform: translateY(100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
  
  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }
  
  .animate-slide-up {
    animation: slide-up 0.3s ease-out;
  }
  
  .animate-scale-up {
    animation: scale-up 0.3s ease-out;
  }
  
  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }
  
  .animate-blink {
    animation: blink 1s infinite;
  }
  
  .animate-spin {
    animation: spin 1s linear infinite;
  }
  
  .animate-flow {
    animation: flow 6s linear infinite;
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  /* Custom range input styles */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

/* 轨道样式 - WebKit */
input[type="range"]::-webkit-slider-runnable-track {
  background: rgba(255, 255, 255, 0.1);
  height: 8px;
  border-radius: 4px;
}

/* 轨道样式 - Firefox */
input[type="range"]::-moz-range-track {
  background: rgba(255, 255, 255, 0.1);
  height: 8px;
  border-radius: 4px;
}

/* 滑块样式 - WebKit */
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: white;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  cursor: pointer;
  margin-top: -4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* 滑块样式 - Firefox */
input[type="range"]::-moz-range-thumb {
  appearance: none;
  background: white;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* 聚焦样式 */
input[type="range"]:focus {
  outline: none;
}

input[type="range"]:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 3px rgba(255,255,255,0.2), 0 2px 4px rgba(0,0,0,0.2);
}

input[type="range"]:focus::-moz-range-thumb {
  box-shadow: 0 0 0 3px rgba(255,255,255,0.2), 0 2px 4px rgba(0,0,0,0.2);
}
`;
document.head.appendChild(style);

export default RemixCreationPage;