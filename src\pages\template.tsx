import React, { useState, useEffect } from 'react';



const template: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [batteryLevel] = useState(85);

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);



  return (
    <div className="relative mx-auto" style={{ maxWidth: '430px', height: '932px' }}>
      {/* 手机框架 */}
      <div className="relative w-full h-full bg-black rounded-[3rem] p-[3px] shadow-2xl">
        <div className="relative w-full h-full bg-gray-900 rounded-[2.8rem] overflow-hidden">
          
          {/* 状态栏 */}
          <div className="absolute top-0 left-0 right-0 z-50 px-8 pt-3 pb-1">
            <div className="flex justify-between items-center text-white text-sm">
              <div className="flex items-center gap-1">
                <span className="font-medium">{currentTime.getHours().toString().padStart(2, '0')}:{currentTime.getMinutes().toString().padStart(2, '0')}</span>
              </div>
              <div className="absolute left-1/2 transform -translate-x-1/2 w-24 h-7 bg-black rounded-full" />
              <div className="flex items-center gap-1">
                <div className="w-4 h-4">
                  <svg viewBox="0 0 24 24" className="w-full h-full fill-white">
                    <path d="M3 7H1v10h2zm4 0H5v10h2zm4 0H9v10h2zm4 0h-2v10h2zm4 0h-2v10h2z"/>
                  </svg>
                </div>
                <div className="w-4 h-4">
                  <svg viewBox="0 0 24 24" className="w-full h-full fill-white">
                    <path d="M2 17h20v2H2z"/>
                    <path d="M3.5 12L2 10.5v-5c0-.55.45-1 1-1h11c.55 0 1 .45 1 1v5L13.5 12z"/>
                  </svg>
                </div>
                <div className="flex items-center">
                  <div className="w-6 h-3 border border-white/50 rounded-sm">
                    <div className="h-full bg-white rounded-sm" style={{ width: `${batteryLevel}%` }} />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 主界面内容区域 */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-white text-center">
              <p className="text-lg">内容区域</p>
            </div>
          </div>

          {/* Home Indicator */}
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white/30 rounded-full" />
        </div>
      </div>
    </div>
  );
};



export default template;
