import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  <PERSON><PERSON>les, Camera, Upload, Image, Music, Hash, Gift,
  ChevronLeft, ChevronRight, Check, Plus, X, Heart,
  Share2, Download, Trophy, Star, Zap, Send, Globe,
  Lock, Users, DollarSign, Calendar, TrendingUp,
  Palette, Wand2, Smile, Volume2, Play, Pause,
  AlertCircle, ChevronDown, ChevronUp, MoreVertical,
  Copy, MessageCircle, Bookmark, ExternalLink,
  Sun, Moon, Cloud, Coffee, Feather, Target, RefreshCw,
  Settings, ArrowLeft
} from 'lucide-react';

// ==================== 类型定义 ====================
interface MoodTheme {
  gradient: string;
  particles: string[];
  glow: string;
  accent: string;
}

interface CoverTemplate {
  id: string;
  name: string;
  gradient: string;
  style: 'dreamy' | 'minimal' | 'retro' | 'cyberpunk' | 'watercolor' | 'oil';
}

interface EmotionTag {
  id: string;
  emoji: string;
  label: string;
  color: string;
  selected: boolean;
}

interface SceneTag {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  category: 'hot' | 'scene' | 'mood';
  selected: boolean;
}

interface PublishSettings {
  visibility: 'public' | 'fans' | 'private';
  allowRemix: boolean;
  allowCommercial: boolean;
  campaigns: string[];
}

interface ShareCard {
  id: string;
  name: string;
  style: 'story' | 'lyrics' | 'wave' | 'minimal';
  preview: string;
}

interface InspirationMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  category: 'lyric' | 'mood' | 'instrument' | 'bpm' | 'style' | 'other';
  highlighted?: boolean;
  selected?: boolean; 
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlocked: boolean;
  reward: string;
}

interface AIGuide {
  message: string;
  mood: 'happy' | 'excited' | 'calm' | 'encouraging' | 'celebration';
  progress?: number;
  suggestion?: string;
}

// ==================== 配色系统 ====================
const moodThemes: Record<string, MoodTheme> = {
  romantic: {
    gradient: 'from-pink-400 via-rose-400 to-red-400',
    particles: ['#FB7185', '#F87171', '#FCA5A5', '#FDA4AF'],
    glow: 'shadow-rose-400/50',
    accent: '#F43F5E'
  },
  happy: {
    gradient: 'from-yellow-400 via-amber-400 to-orange-400',
    particles: ['#FCD34D', '#FB923C', '#FBBF24', '#F59E0B'],
    glow: 'shadow-amber-400/50',
    accent: '#F59E0B'
  },
  calm: {
    gradient: 'from-blue-400 via-cyan-400 to-teal-400',
    particles: ['#60A5FA', '#06B6D4', '#14B8A6', '#10B981'],
    glow: 'shadow-cyan-400/50',
    accent: '#06B6D4'
  },
  creative: {
    gradient: 'from-purple-400 via-violet-400 to-indigo-400',
    particles: ['#C084FC', '#A78BFA', '#818CF8', '#6366F1'],
    glow: 'shadow-purple-400/50',
    accent: '#A78BFA'
  }
};

// ==================== 数据配置 ====================
const coverTemplates: CoverTemplate[] = [
  { id: 'ai-1', name: '梦幻', gradient: 'from-purple-400 via-pink-400 to-rose-400', style: 'dreamy' },
  { id: 'ai-2', name: '极简', gradient: 'from-gray-200 via-gray-100 to-white', style: 'minimal' },
  { id: 'ai-3', name: '复古', gradient: 'from-amber-400 via-orange-400 to-red-400', style: 'retro' },
  { id: 'ai-4', name: '赛博', gradient: 'from-cyan-400 via-blue-500 to-purple-500', style: 'cyberpunk' }
];

const emotionTags: EmotionTag[] = [
  { id: 'happy', emoji: '😊', label: '开心', color: 'bg-yellow-400/20', selected: false },
  { id: 'sad', emoji: '😢', label: '忧伤', color: 'bg-blue-400/20', selected: false },
  { id: 'excited', emoji: '🔥', label: '燃', color: 'bg-red-400/20', selected: false },
  { id: 'healing', emoji: '💚', label: '治愈', color: 'bg-green-400/20', selected: false },
  { id: 'romantic', emoji: '💕', label: '浪漫', color: 'bg-pink-400/20', selected: false },
  { id: 'calm', emoji: '😴', label: '平静', color: 'bg-indigo-400/20', selected: false },
  { id: 'thinking', emoji: '🤔', label: '思考', color: 'bg-purple-400/20', selected: false },
  { id: 'cool', emoji: '😎', label: '酷炫', color: 'bg-slate-400/20', selected: false }
];

const sceneTags: SceneTag[] = [
  { id: 'summer', label: '#夏日限定', icon: Sun, category: 'hot', selected: false },
  { id: 'graduation', label: '#毕业季', icon: Trophy, category: 'hot', selected: false },
  { id: 'night', label: '#深夜emo', icon: Moon, category: 'hot', selected: false },
  { id: 'commute', label: '#通勤路上', icon: Target, category: 'scene', selected: false },
  { id: 'gym', label: '#健身房', icon: Zap, category: 'scene', selected: false },
  { id: 'cafe', label: '#咖啡厅', icon: Coffee, category: 'scene', selected: false },
  { id: 'miss', label: '#想念', icon: Heart, category: 'mood', selected: false },
  { id: 'letgo', label: '#释怀', icon: Feather, category: 'mood', selected: false },
  { id: 'restart', label: '#重新开始', icon: RefreshCw, category: 'mood', selected: false }
];

const shareCardTemplates: ShareCard[] = [
  { id: 'story', name: '故事版', style: 'story', preview: 'gradient-1' },
  { id: 'lyrics', name: '歌词版', style: 'lyrics', preview: 'gradient-2' },
  { id: 'wave', name: '波形版', style: 'wave', preview: 'gradient-3' },
  { id: 'minimal', name: '极简版', style: 'minimal', preview: 'gradient-4' }
];

// 模拟灵感对话数据
const mockInspirationMessages: InspirationMessage[] = [
  {
    id: 'ins-1',
    type: 'user',
    content: '小弦你好，我想创作一首关于夏天的歌，能给我一些建议吗？',
    timestamp: new Date('2024-08-01 14:30'),
    category: 'mood',
    highlighted: true,
    selected: true
  },
  {
    id: 'ins-2',
    type: 'ai',
    content: '你好！夏天的歌真棒！我可以帮你构思。你心中的夏天是什么感觉的？是热情的海滩，还是安静的傍晚？',
    timestamp: new Date('2024-08-01 14:31'),
    category: 'mood',
    highlighted: true,
    selected: true
  },
  {
    id: 'ins-3',
    type: 'user',
    content: '我想表达的是夏日晚风的感觉，那种温柔又治愈的氛围',
    timestamp: new Date('2024-08-01 14:33'),
    category: 'mood',
    highlighted: true,
    selected: true
  },
  {
    id: 'ins-4',
    type: 'ai',
    content: '很棒的构思！我建议用BPM 80-90的节奏，搭配吉他为主旋律，再加入一些海浪和白噪音的环境音。你觉得呢？',
    timestamp: new Date('2024-08-01 14:35'),
    category: 'instrument',
    selected: false
  },
  {
    id: 'ins-5',
    type: 'user',
    content: '好主意！我还想加入一些关于星星的元素，夜晚的星空很美',
    timestamp: new Date('2024-08-01 14:37'),
    category: 'lyric',
    selected: false
  },
  {
    id: 'ins-6',
    type: 'ai',
    content: '星星是很好的意象！我想到一句歌词："星星开始在天空闪烁，照亮了回家的路"。你觉得如何？',
    timestamp: new Date('2024-08-01 14:38'),
    category: 'lyric',
    highlighted: true,
    selected: true
  }
];

// ==================== 主组件 ====================
const PublishWork: React.FC = () => {
  // ========== 状态管理 ==========
  const [currentMood, setCurrentMood] = useState<string>('romantic');
  const [currentStep, setCurrentStep] = useState<'edit' | 'publishing' | 'success'>('edit');
  const [completionProgress, setCompletionProgress] = useState(0);
  
  // AI小弦状态
  const [aiGuide, setAiGuide] = useState<AIGuide>({
    message: "准备好让世界听见你的声音了吗？我来帮你完善作品信息 ✨",
    mood: 'excited'
  });
  const [aiMessage, setAiMessage] = useState('');
  const [showAiChat, setShowAiChat] = useState(false);
  const [aiTypingSpeed] = useState(50);
  
  // 作品信息
  const [workTitle, setWorkTitle] = useState('');
  const [workStory, setWorkStory] = useState('');
  const [selectedEmotions, setSelectedEmotions] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [dedicateTo, setDedicateTo] = useState('');
  const [generateCard, setGenerateCard] = useState(true);
  
  // 封面相关
  const [selectedCover, setSelectedCover] = useState<CoverTemplate>(coverTemplates[0]);
  const [coverSource, setCoverSource] = useState<'ai' | 'album' | 'camera' | 'library'>('ai');
  const [isGeneratingCover, setIsGeneratingCover] = useState(false);

  // ========== 灵感回响相关状态 ==========
  const [includeInspiration, setIncludeInspiration] = useState(true); 
  const [showInspirationModal, setShowInspirationModal] = useState(false); 
  const [selectedDialogues, setSelectedDialogues] = useState<string[]>(() => {
    // 默认选择高亮的对话
    return mockInspirationMessages
      .filter(msg => msg.highlighted)
      .map(msg => msg.id);
  }); 
  const [inspirationMessages] = useState<InspirationMessage[]>(mockInspirationMessages); 
  
  // 发布设置
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [publishSettings, setPublishSettings] = useState<PublishSettings>({
    visibility: 'public',
    allowRemix: true,
    allowCommercial: false,
    campaigns: []
  });
  
  // 发布流程
  const [publishProgress, setPublishProgress] = useState(0);
  const [publishStage, setPublishStage] = useState('');
  
  // 分享卡片
  const [selectedShareCard, setSelectedShareCard] = useState<ShareCard>(shareCardTemplates[0]);
  const [showShareOptions, setShowShareOptions] = useState(false);
  
  // 成就系统
  const [unlockedAchievement, setUnlockedAchievement] = useState<Achievement | null>(null);
  
  // Refs
  const typingTimer = useRef<NodeJS.Timeout | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // ========== 生命周期 ==========
  useEffect(() => {
    // 打字机效果
    if (aiGuide.message) {
      setAiMessage('');
      let index = 0;
      const text = aiGuide.message;
      
      const typeChar = () => {
        if (index < text.length) {
          setAiMessage(text.substring(0, index + 1));
          index++;
          typingTimer.current = setTimeout(typeChar, aiTypingSpeed);
        }
      };
      
      typingTimer.current = setTimeout(typeChar, 300);
      
      return () => {
        if (typingTimer.current) {
          clearTimeout(typingTimer.current);
        }
      };
    }
  }, [aiGuide, aiTypingSpeed]);

  // 监听完成度
  useEffect(() => {
    calculateProgress();
  }, [workTitle, workStory, selectedEmotions, selectedTags, dedicateTo]);

  // ========== 进度计算 ==========
  const calculateProgress = useCallback(() => {
    let progress = 0;
    if (workTitle) progress += 25;
    if (workStory) progress += 25;
    if (selectedEmotions.length > 0) progress += 25;
    if (selectedTags.length > 0) progress += 25;
    
    setCompletionProgress(progress);
    
    // 更新AI引导
    if (progress === 0) {
      setAiGuide({
        message: "让我们开始吧！先给你的作品起个好听的名字 🎵",
        mood: 'encouraging',
        progress
      });
    } else if (progress === 25) {
      setAiGuide({
        message: "名字很棒！再讲讲创作背后的故事吧 📝",
        mood: 'happy',
        progress
      });
    } else if (progress === 50) {
      setAiGuide({
        message: "故事很动人！选择一些情绪标签吧 💭",
        mood: 'encouraging',
        progress
      });
    } else if (progress === 75) {
      setAiGuide({
        message: "就快完成了！再添加一些场景标签 🏷️",
        mood: 'excited',
        progress
      });
    } else if (progress === 100) {
      setAiGuide({
        message: "完美！你的作品已经准备好发布了 🎉",
        mood: 'celebration',
        progress
      });
    }
  }, [workTitle, workStory, selectedEmotions, selectedTags]);

  // ========== 封面处理 ==========
  const handleCoverGenerate = useCallback(() => {
    setIsGeneratingCover(true);
    setAiGuide({
      message: "正在为你生成独特的封面...这会很酷的！🎨",
      mood: 'calm'
    });
    
    setTimeout(() => {
      setIsGeneratingCover(false);
      // 随机选择一个模板
      const randomTemplate = coverTemplates[Math.floor(Math.random() * coverTemplates.length)];
      setSelectedCover(randomTemplate);
      setAiGuide({
        message: "封面生成完成！感觉怎么样？不满意可以重新生成哦 ✨",
        mood: 'happy'
      });
    }, 2000);
  }, []);

  const handleCoverUpload = useCallback((source: 'album' | 'camera' | 'library') => {
    setCoverSource(source);
    if (source === 'album' || source === 'library') {
      fileInputRef.current?.click();
    }
    setAiGuide({
      message: `正在从${source === 'album' ? '相册' : source === 'camera' ? '相机' : '素材库'}选择封面...`,
      mood: 'calm'
    });
  }, []);

  // ========== 输入框焦点处理 ==========
  const handleInputFocus = useCallback((field: string) => {
    const helps: Record<string, string> = {
      title: "给作品起个独特的名字吧！比如《夏日晚风》《青春日记》《雨后》",
      story: "可以写写灵感来源、创作时的心情、想对听众说的话...",
      dedicate: "这首歌想要献给谁呢？可以是某个人，也可以是某个时刻的自己"
    };
    
    setAiGuide({
      message: helps[field] || "需要我帮忙吗？",
      mood: 'happy',
      suggestion: field
    });
  }, []);

  // ========== 情绪和标签处理 ==========
  const handleEmotionToggle = useCallback((emotionId: string) => {
    setSelectedEmotions(prev => {
      const newEmotions = prev.includes(emotionId)
        ? prev.filter(id => id !== emotionId)
        : prev.length < 3
        ? [...prev, emotionId]
        : prev;
      
      if (newEmotions.length === 3 && !prev.includes(emotionId)) {
        setAiGuide({
          message: "最多选择3个情绪标签哦，这样更容易让听众理解你的心情 💭",
          mood: 'calm'
        });
      }
      
      return newEmotions;
    });
  }, []);

  const handleTagToggle = useCallback((tagId: string) => {
    setSelectedTags(prev => {
      const newTags = prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId];
      
      if (newTags.length === 1) {
        const tag = sceneTags.find(t => t.id === tagId);
        if (tag?.category === 'hot') {
          setAiGuide({
            message: `加入了${tag.label}！这个话题正火热，会有更多人发现你的作品 🔥`,
            mood: 'excited'
          });
        }
      }
      
      return newTags;
    });
  }, []);

  // ========== 发布处理 ==========
  const handlePublish = useCallback(async () => {
    if (completionProgress < 50) {
      setAiGuide({
        message: "作品信息还不够完整，再完善一下吧，这样能获得更多曝光哦 💪",
        mood: 'encouraging'
      });
      return;
    }

    setCurrentStep('publishing');
    setPublishStage('上传中');
    setPublishProgress(0);
    
    // 模拟发布过程
    const stages = [
      { progress: 20, stage: '上传中', message: '正在将你的音乐送往云端...' },
      { progress: 40, stage: '处理中', message: '为你的作品镀上金边...' },
      { progress: 60, stage: '优化中', message: '添加魔法效果中...' },
      { progress: 80, stage: '发布中', message: '准备惊艳全世界...' },
      { progress: 100, stage: '完成', message: '🎉 恭喜！你的音乐已经发布！' }
    ];

    for (const stage of stages) {
      await new Promise(resolve => setTimeout(resolve, 800));
      setPublishProgress(stage.progress);
      setPublishStage(stage.stage);
      setAiGuide({
        message: stage.message,
        mood: stage.progress === 100 ? 'celebration' : 'calm'
      });
    }

    // 发布成功
    setTimeout(() => {
      setCurrentStep('success');
      setUnlockedAchievement({
        id: 'first-publish',
        title: '初露锋芒',
        description: '发布了第一首作品',
        icon: '🏆',
        unlocked: true,
        reward: '音符币 +50'
      });
    }, 500);
  }, [completionProgress]);

  // ========== 分享处理 ==========
  const handleShare = useCallback((platform: 'wechat' | 'moments' | 'xiaohongshu') => {
    const messages = {
      wechat: "正在生成微信分享卡片...",
      moments: "正在生成朋友圈分享图...",
      xiaohongshu: "正在生成小红书笔记..."
    };
    
    setAiGuide({
      message: messages[platform],
      mood: 'calm'
    });
    
    setTimeout(() => {
      setAiGuide({
        message: "分享卡片已生成！快去分享你的作品吧 🎊",
        mood: 'celebration'
      });
    }, 1500);
  }, []);

  // ========== 返回处理 ==========
  const handleBack = useCallback(() => {
    if (currentStep === 'success') {
      setCurrentStep('edit');
    } else {
      // 返回上一页的逻辑
      console.log('返回上一页');
    }
  }, [currentStep]);

  // ========== AI聊天处理 ==========
  const handleAiChat = useCallback(() => {
    setShowAiChat(!showAiChat);
    if (!showAiChat) {
      setAiGuide({
        message: "有什么需要帮助的吗？我可以帮你完善作品信息 💬",
        mood: 'happy'
      });
    }
  }, [showAiChat]);

  // ========== 渲染组件 ==========
  
  // 编辑页面
  const renderEditPage = () => (
    <div className="px-6 pb-24"> {/* 减少底部padding给按钮更多空间 */}
      {/* 作品展示区 */}
      <div className="mb-6">
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 overflow-hidden">
          {/* 封面预览 */}
          <div className={`relative h-64 bg-gradient-to-br ${selectedCover.gradient} flex items-center justify-center`}>
            {isGeneratingCover ? (
              <div className="flex flex-col items-center gap-3">
                <div className="w-12 h-12 border-3 border-white/30 border-t-white rounded-full animate-spin" />
                <span className="text-white/80 text-sm">AI生成中...</span>
              </div>
            ) : (
              <>
                <Music className="w-20 h-20 text-white/30" />
                {/* 动态波形 */}
                <div className="absolute bottom-0 left-0 right-0 flex items-end justify-around h-20 px-4">
                  {Array.from({ length: 20 }, (_, i) => (
                    <div
                      key={i}
                      className="w-1 bg-white/40 rounded-full animate-wave"
                      style={{
                        height: `${Math.random() * 60 + 20}%`,
                        animationDelay: `${i * 50}ms`
                      }}
                    />
                  ))}
                </div>
              </>
            )}
            
            {/* 播放按钮 */}
            <button className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-black/30 backdrop-blur rounded-full flex items-center justify-center hover:scale-110 transition-transform">
              <Play className="w-8 h-8 text-white ml-1" />
            </button>
          </div>
          
          {/* 封面选择器 */}
          <div className="p-4 border-t border-white/10">
            <div className="mb-3">
              <h3 className="text-white/80 text-sm font-medium mb-1">选择封面</h3>
              <p className="text-white/50 text-xs">为你的音乐作品选择一个吸引人的封面</p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={handleCoverGenerate}
                className={`flex-1 py-3 px-4 rounded-xl text-sm font-medium transition-all flex items-center justify-center gap-2 ${
                  coverSource === 'ai'
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg shadow-purple-500/25'
                    : 'bg-white/10 text-white/70 hover:bg-white/20 hover:scale-105'
                }`}
              >
                <Wand2 className="w-4 h-4" />
                AI生成
              </button>
              <button
                onClick={() => handleCoverUpload('album')}
                className="flex-1 py-3 px-4 bg-white/10 hover:bg-white/20 hover:scale-105 rounded-xl text-white/70 text-sm transition-all flex items-center justify-center gap-2"
              >
                <Image className="w-4 h-4" />
                相册
              </button>
              <button
                onClick={() => handleCoverUpload('camera')}
                className="flex-1 py-3 px-4 bg-white/10 hover:bg-white/20 hover:scale-105 rounded-xl text-white/70 text-sm transition-all flex items-center justify-center gap-2"
              >
                <Camera className="w-4 h-4" />
                拍照
              </button>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              className="hidden"
              onChange={(e) => {
                if (e.target.files?.[0]) {
                  setAiGuide({
                    message: "封面上传成功！看起来很不错 👍",
                    mood: 'happy'
                  });
                }
              }}
            />
          </div>
        </div>
      </div>

      {/* 故事编织区 */}
      <div className="space-y-4 mb-6">
        {/* 作品标题 */}
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
          <label className="text-white font-medium text-sm flex items-center gap-2 mb-2">
            作品标题
            <span className="text-red-400">*</span>
          </label>
          <input
            type="text"
            value={workTitle}
            onChange={(e) => setWorkTitle(e.target.value)}
            onFocus={() => handleInputFocus('title')}
            placeholder="给你的作品起个好听的名字..."
            className="w-full bg-white/10 rounded-xl px-4 py-3 text-white placeholder-white/40 outline-none focus:bg-white/15 transition-all"
            maxLength={30}
          />
          <div className="mt-2 flex justify-between text-xs text-white/40">
            <span>建议8-15字</span>
            <span>{workTitle.length}/30</span>
          </div>
        </div>

        {/* 创作故事 - 合并为一个输入框 */}
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
          <label className="text-white font-medium text-sm flex items-center gap-2 mb-3">
            创作故事
            <span className="text-red-400">*</span>
          </label>
          
          <textarea
            value={workStory}
            onChange={(e) => setWorkStory(e.target.value)}
            onFocus={() => handleInputFocus('story')}
            placeholder="分享这首歌的灵感来源、创作时的心情、想对听众说的话..."
            className="w-full bg-white/10 rounded-xl px-4 py-3 text-white placeholder-white/40 outline-none focus:bg-white/15 transition-all resize-none"
            rows={6}
          />
          
          <div className="mt-2 text-xs text-white/40">
            💡 真诚的故事更能打动听众
          </div>
        </div>

        {/* 灵感回响配置 */}
        {renderInspirationConfig()}

        {/* 情绪画板 */}
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
          <div className="flex items-center justify-between mb-3">
            <label className="text-white font-medium text-sm">
              选择作品情绪（最多3个）
            </label>
            <span className="text-xs text-white/40">{selectedEmotions.length}/3</span>
          </div>
          
          <div className="grid grid-cols-4 gap-3">
            {emotionTags.map(emotion => (
              <button
                key={emotion.id}
                onClick={() => handleEmotionToggle(emotion.id)}
                className={`py-3 rounded-xl transition-all ${
                  selectedEmotions.includes(emotion.id)
                    ? `${emotion.color} border-2 border-white/30 scale-105`
                    : 'bg-white/10 hover:bg-white/15 border-2 border-transparent'
                }`}
              >
                <div className="flex flex-col items-center gap-1">
                  <span className="text-2xl">{emotion.emoji}</span>
                  <span className="text-xs text-white/80">{emotion.label}</span>
                </div>
              </button>
            ))}
          </div>
          
          {selectedEmotions.length > 0 && (
            <div className="mt-3 p-2 bg-white/5 rounded-lg">
              <p className="text-xs text-white/60">
                小弦感知：这首歌充满了
                {selectedEmotions.map(id => 
                  emotionTags.find(e => e.id === id)?.label
                ).join('、')}
                的情绪
              </p>
            </div>
          )}
        </div>

        {/* 场景标签 */}
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
          <label className="text-white font-medium text-sm block mb-3">
            添加标签 #
          </label>
          
          {/* 热门标签 */}
          <div className="mb-3">
            <p className="text-xs text-white/60 mb-2 flex items-center gap-1">
              🔥 热门标签
            </p>
            <div className="flex flex-wrap gap-2">
              {sceneTags.filter(tag => tag.category === 'hot').map(tag => (
                <button
                  key={tag.id}
                  onClick={() => handleTagToggle(tag.id)}
                  className={`px-3 py-1.5 rounded-full text-sm transition-all ${
                    selectedTags.includes(tag.id)
                      ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white'
                      : 'bg-white/10 text-white/70 hover:bg-white/15'
                  }`}
                >
                  {tag.label}
                </button>
              ))}
            </div>
          </div>
          
          {/* 场景标签 */}
          <div className="mb-3">
            <p className="text-xs text-white/60 mb-2 flex items-center gap-1">
              📍 场景标签
            </p>
            <div className="flex flex-wrap gap-2">
              {sceneTags.filter(tag => tag.category === 'scene').map(tag => (
                <button
                  key={tag.id}
                  onClick={() => handleTagToggle(tag.id)}
                  className={`px-3 py-1.5 rounded-full text-sm transition-all ${
                    selectedTags.includes(tag.id)
                      ? 'bg-white/20 text-white border border-white/30'
                      : 'bg-white/10 text-white/70 hover:bg-white/15'
                  }`}
                >
                  {tag.label}
                </button>
              ))}
            </div>
          </div>
          
          {/* 心情标签 */}
          <div>
            <p className="text-xs text-white/60 mb-2 flex items-center gap-1">
              💭 心情标签
            </p>
            <div className="flex flex-wrap gap-2">
              {sceneTags.filter(tag => tag.category === 'mood').map(tag => (
                <button
                  key={tag.id}
                  onClick={() => handleTagToggle(tag.id)}
                  className={`px-3 py-1.5 rounded-full text-sm transition-all ${
                    selectedTags.includes(tag.id)
                      ? 'bg-white/20 text-white border border-white/30'
                      : 'bg-white/10 text-white/70 hover:bg-white/15'
                  }`}
                >
                  {tag.label}
                </button>
              ))}
            </div>
          </div>
          
          {selectedTags.length > 0 && (
            <div className="mt-3 text-xs text-white/60">
              已选：{selectedTags.map(id => 
                sceneTags.find(t => t.id === id)?.label
              ).join(' ')}
            </div>
          )}
        </div>

        {/* 献给... */}
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
          <label className="text-white font-medium text-sm block mb-3">
            这首歌，想要献给...（选填）
          </label>
          
          <div className="flex gap-2 mb-3">
            <button
              onClick={() => setDedicateTo('@好友')}
              className="px-3 py-1.5 bg-white/10 hover:bg-white/15 rounded-full text-white/70 text-sm transition-all"
            >
              @好友
            </button>
            <button
              onClick={() => setDedicateTo('远方的TA')}
              className="px-3 py-1.5 bg-white/10 hover:bg-white/15 rounded-full text-white/70 text-sm transition-all"
            >
              远方的TA
            </button>
            <button
              onClick={() => setDedicateTo('曾经的自己')}
              className="px-3 py-1.5 bg-white/10 hover:bg-white/15 rounded-full text-white/70 text-sm transition-all"
            >
              曾经的自己
            </button>
          </div>
          
          <input
            type="text"
            value={dedicateTo}
            onChange={(e) => setDedicateTo(e.target.value)}
            onFocus={() => handleInputFocus('dedicate')}
            placeholder="献给所有深夜还在努力的人..."
            className="w-full bg-white/10 rounded-xl px-4 py-3 text-white placeholder-white/40 outline-none focus:bg-white/15 transition-all"
          />
          
          {dedicateTo && (
            <div className="mt-3 flex items-center gap-2">
              <input
                type="checkbox"
                id="generateCard"
                checked={generateCard}
                onChange={(e) => setGenerateCard(e.target.checked)}
                className="w-4 h-4 rounded bg-white/10 border-white/30"
              />
              <label htmlFor="generateCard" className="text-sm text-white/70">
                发布成功后生成音乐贺卡
              </label>
            </div>
          )}
        </div>

        {/* 发布设置（高级选项） */}
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 overflow-hidden">
          <button
            onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
            className="w-full px-4 py-3 flex items-center justify-between text-white hover:bg-white/5 transition-all"
          >
            <span className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              发布设置
            </span>
            <ChevronDown className={`w-4 h-4 transition-transform ${
              showAdvancedSettings ? 'rotate-180' : ''
            }`} />
          </button>
          
          {showAdvancedSettings && (
            <div className="px-4 pb-4 space-y-4 border-t border-white/10">
              {/* 发布范围 */}
              <div className="pt-3">
                <label className="text-white/60 text-xs mb-2 block flex items-center gap-2">
                  <Globe className="w-3 h-3" />
                  发布范围
                </label>
                <div className="flex gap-2">
                  <button
                    onClick={() => setPublishSettings({ ...publishSettings, visibility: 'public' })}
                    className={`flex-1 py-2 rounded-xl text-sm transition-all ${
                      publishSettings.visibility === 'public'
                        ? 'bg-white/20 text-white'
                        : 'bg-white/10 text-white/60 hover:bg-white/15'
                    }`}
                  >
                    公开
                  </button>
                  <button
                    onClick={() => setPublishSettings({ ...publishSettings, visibility: 'fans' })}
                    className={`flex-1 py-2 rounded-xl text-sm transition-all ${
                      publishSettings.visibility === 'fans'
                        ? 'bg-white/20 text-white'
                        : 'bg-white/10 text-white/60 hover:bg-white/15'
                    }`}
                  >
                    粉丝可见
                  </button>
                  <button
                    onClick={() => setPublishSettings({ ...publishSettings, visibility: 'private' })}
                    className={`flex-1 py-2 rounded-xl text-sm transition-all ${
                      publishSettings.visibility === 'private'
                        ? 'bg-white/20 text-white'
                        : 'bg-white/10 text-white/60 hover:bg-white/15'
                    }`}
                  >
                    仅自己
                  </button>
                </div>
              </div>
              
              {/* 版权设置 */}
              <div>
                <label className="text-white/60 text-xs mb-2 block flex items-center gap-2">
                  <DollarSign className="w-3 h-3" />
                  版权设置
                </label>
                <div className="space-y-2">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="allowRemix"
                      checked={publishSettings.allowRemix}
                      onChange={(e) => setPublishSettings({ ...publishSettings, allowRemix: e.target.checked })}
                      className="w-4 h-4 rounded bg-white/10 border-white/30"
                    />
                    <label htmlFor="allowRemix" className="text-sm text-white/70">允许二创</label>
                  </div>
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="allowCommercial"
                      checked={publishSettings.allowCommercial}
                      onChange={(e) => setPublishSettings({ ...publishSettings, allowCommercial: e.target.checked })}
                      className="w-4 h-4 rounded bg-white/10 border-white/30"
                    />
                    <label htmlFor="allowCommercial" className="text-sm text-white/70">允许商用</label>
                  </div>
                </div>
              </div>
              
              {/* 参与活动 */}
              <div>
                <label className="text-white/60 text-xs mb-2 block flex items-center gap-2">
                  <TrendingUp className="w-3 h-3" />
                  参与活动
                </label>
                <div className="space-y-2">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="summer"
                      className="w-4 h-4 rounded bg-white/10 border-white/30"
                    />
                    <label htmlFor="summer" className="text-sm text-white/70">#夏日音乐节</label>
                  </div>
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="newbie"
                      className="w-4 h-4 rounded bg-white/10 border-white/30"
                    />
                    <label htmlFor="newbie" className="text-sm text-white/70">#新人扶持计划</label>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  // 发布中页面
  const renderPublishingPage = () => (
    <div className="flex flex-col items-center justify-center h-full px-6">
      <div className="text-center">
        {/* 动画图标 */}
        <div className="relative w-32 h-32 mx-auto mb-8">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse" />
          <div className="absolute inset-2 bg-gray-900 rounded-full flex items-center justify-center">
            {publishProgress < 100 ? (
              <div className="relative">
                <Music className="w-12 h-12 text-white animate-bounce" />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-ping" />
              </div>
            ) : (
              <Check className="w-12 h-12 text-green-400 animate-scale-up" />
            )}
          </div>
          
          {/* 进度环 */}
          <svg className="absolute inset-0 w-32 h-32 transform -rotate-90">
            <circle
              cx="64"
              cy="64"
              r="60"
              stroke="rgba(255,255,255,0.1)"
              strokeWidth="4"
              fill="none"
            />
            <circle
              cx="64"
              cy="64"
              r="60"
              stroke="url(#gradient)"
              strokeWidth="4"
              fill="none"
              strokeDasharray={`${publishProgress * 3.77} 377`}
              strokeLinecap="round"
              className="transition-all duration-500"
            />
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#A855F7" />
                <stop offset="100%" stopColor="#EC4899" />
              </linearGradient>
            </defs>
          </svg>
        </div>
        
        {/* 状态文字 */}
        <h2 className="text-white text-xl font-medium mb-2">{publishStage}</h2>
        <p className="text-white/60 text-sm mb-8">{aiGuide.message}</p>
        
        {/* 进度百分比 */}
        <div className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
          {publishProgress}%
        </div>
        
        {/* 音符动画 */}
        <div className="mt-8 flex justify-center gap-2">
          {[...Array(5)].map((_, i) => (
            <div
              key={i}
              className="w-2 h-8 bg-gradient-to-t from-purple-400 to-pink-400 rounded-full animate-wave"
              style={{ animationDelay: `${i * 100}ms` }}
            />
          ))}
        </div>
      </div>
    </div>
  );

  // 成功页面
  const renderSuccessPage = () => (
    <div className="px-6 py-8 pb-24"> {/* 减少底部padding给按钮更多空间 */}
      {/* 成就解锁 */}
      {unlockedAchievement && (
        <div className="mb-6 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-500/30 animate-scale-up">
          <div className="text-center">
            <div className="text-5xl mb-3">{unlockedAchievement.icon}</div>
            <h3 className="text-white text-lg font-medium mb-1">
              解锁成就：{unlockedAchievement.title}
            </h3>
            <p className="text-white/60 text-sm mb-3">{unlockedAchievement.description}</p>
            <div className="inline-flex items-center gap-2 px-3 py-1 bg-yellow-500/20 rounded-full">
              <Gift className="w-4 h-4 text-yellow-400" />
              <span className="text-yellow-400 text-sm">{unlockedAchievement.reward}</span>
            </div>
          </div>
        </div>
      )}
      
      {/* 分享卡片预览 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-white font-medium flex items-center gap-2">
            <Share2 className="w-5 h-5" />
            分享卡片
          </h3>
          <button className="text-xs text-purple-400 hover:text-purple-300">
            切换样式
          </button>
        </div>
        
        {/* 卡片轮播 */}
        <div className="relative">
          <div className="bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl p-1">
            <div className="bg-gray-900 rounded-xl p-4">
              <div className="aspect-square bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-xl flex items-center justify-center mb-4">
                <Music className="w-20 h-20 text-white/30" />
              </div>
              <h4 className="text-white font-medium mb-2">{workTitle || '我的音乐作品'}</h4>
              <p className="text-white/60 text-sm mb-3 line-clamp-2">
                {workStory || '用音乐记录生活的美好瞬间'}
              </p>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <img src="/api/placeholder/32/32" alt="QR" className="w-8 h-8 rounded bg-white/10" />
                  <span className="text-xs text-white/40">扫码收听</span>
                </div>
                <div className="text-xs text-white/40">心弦 MoodBeat</div>
              </div>
            </div>
          </div>
          
          {/* 样式选择器 */}
          <div className="flex justify-center gap-2 mt-3">
            {shareCardTemplates.map((template) => (
              <button
                key={template.id}
                onClick={() => setSelectedShareCard(template)}
                className={`w-2 h-2 rounded-full transition-all ${
                  selectedShareCard.id === template.id
                    ? 'w-6 bg-white'
                    : 'bg-white/40'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
      
      {/* 分享按钮 */}
      <div className="space-y-3">
        <button
          onClick={() => handleShare('wechat')}
          className="w-full py-3 bg-green-500/20 hover:bg-green-500/30 rounded-xl text-green-400 font-medium transition-all flex items-center justify-center gap-2"
        >
          <MessageCircle className="w-5 h-5" />
          分享给微信好友
        </button>
        <button
          onClick={() => handleShare('moments')}
          className="w-full py-3 bg-green-500/20 hover:bg-green-500/30 rounded-xl text-green-400 font-medium transition-all flex items-center justify-center gap-2"
        >
          <Users className="w-5 h-5" />
          分享到朋友圈
        </button>
        <button
          onClick={() => handleShare('xiaohongshu')}
          className="w-full py-3 bg-red-500/20 hover:bg-red-500/30 rounded-xl text-red-400 font-medium transition-all flex items-center justify-center gap-2"
        >
          <Heart className="w-5 h-5" />
          分享到小红书
        </button>
      </div>
      
      {/* 查看作品按钮 */}
      <div className="mt-6">
        <button className="w-full py-3 bg-white/10 hover:bg-white/15 rounded-xl text-white/70 font-medium transition-all">
          查看作品
        </button>
      </div>
    </div>
  );
  
  // 灵感回响
  const renderInspirationConfig = () => (
    <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
      {/* 标题和开关 */}
      <div className="flex items-center justify-between mb-3">
        <label className="text-white font-medium text-sm flex items-center gap-2">
          <Sparkles className="w-4 h-4 text-purple-400" />
          灵感回响
          <span className="text-xs text-white/50">（选填）</span>
        </label>
        
        {/* 开关按钮 */}
        <button
          onClick={() => {
            setIncludeInspiration(!includeInspiration);
            if (!includeInspiration && selectedDialogues.length === 0) {
              // 首次开启时，自动选择高亮对话
              const highlightedIds = inspirationMessages
                .filter(msg => msg.highlighted)
                .map(msg => msg.id);
              setSelectedDialogues(highlightedIds);
              setAiGuide({
                message: "灵感回响已开启！我为你精选了一些精彩对话，你可以点击编辑来调整 ✨",
                mood: 'happy'
              });
            }
          }}
          className={`relative w-14 h-7 rounded-full transition-all ${
            includeInspiration 
              ? 'bg-gradient-to-r from-purple-500 to-pink-500' 
              : 'bg-white/20'
          }`}
        >
          <div className={`absolute top-1 w-5 h-5 bg-white rounded-full transition-transform ${
            includeInspiration ? 'translate-x-8' : 'translate-x-1'
          }`} />
        </button>
      </div>
      
      {/* 说明文字 */}
      <p className="text-white/50 text-xs mb-3">
        展示你与AI小弦的创作对话，让听众了解作品背后的灵感故事
      </p>
      
      {/* 开启时显示内容预览和编辑 */}
      {includeInspiration && (
        <>
          {/* 预览区域 */}
          <div className="bg-white/5 rounded-xl p-3 mb-3 max-h-40 overflow-y-auto no-scrollbar">
            <div className="space-y-3">
              {inspirationMessages
                .filter(msg => selectedDialogues.includes(msg.id))
                .slice(0, 3)
                .map(message => (
                  <div key={message.id} className="flex gap-2">
                    <div className={`w-6 h-6 rounded-full flex-shrink-0 flex items-center justify-center ${
                      message.type === 'user' 
                        ? 'bg-gradient-to-br from-blue-400 to-cyan-400' 
                        : 'bg-gradient-to-br from-purple-400 to-pink-400'
                    }`}>
                      {message.type === 'user' ? (
                        <span className="text-white text-[10px] font-bold">我</span>
                      ) : (
                        <Sparkles className="w-3 h-3 text-white" />
                      )}
                    </div>
                    <div className="flex-1">
                      <p className="text-white/70 text-xs leading-relaxed line-clamp-2">
                        {message.content}
                      </p>
                    </div>
                  </div>
                ))}
              
              {selectedDialogues.length > 3 && (
                <div className="text-center text-white/40 text-xs">
                  还有 {selectedDialogues.length - 3} 条对话...
                </div>
              )}
            </div>
          </div>
          
          {/* 操作按钮 */}
          <div className="flex gap-2">
            <button
              onClick={() => {
                setShowInspirationModal(true);
                setAiGuide({
                  message: "选择你想要展示的精彩对话片段，让听众感受创作的魅力 🎵",
                  mood: 'encouraging'
                });
              }}
              className="flex-1 py-2 bg-white/10 hover:bg-white/15 rounded-xl text-white/80 text-sm transition-all flex items-center justify-center gap-2"
            >
              <MessageCircle className="w-4 h-4" />
              编辑对话
            </button>
          </div>
          
          {/* 统计信息 */}
          {selectedDialogues.length > 0 && (
            <div className="mt-3 pt-3 border-t border-white/10 flex items-center justify-between text-xs text-white/40">
              <span>已选择 {selectedDialogues.length} 条对话</span>
              <span className="flex items-center gap-1">
                <Users className="w-3 h-3" />
                预计增加30%互动
              </span>
            </div>
          )}
        </>
      )}
    </div>
  );

  // 灵感对话选择模态框
  const renderInspirationModal = () => (
    showInspirationModal && (
      <div className="absolute inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-end animate-fade-in">
        <div className="w-full bg-gray-900 rounded-t-3xl animate-slide-up" style={{ maxHeight: '85%' }}>
          {/* 拖动指示条 */}
          <div className="flex justify-center pt-3 pb-2">
            <div className="w-12 h-1.5 bg-white/20 rounded-full" />
          </div>
          
          {/* 标题栏 */}
          <div className="px-6 pb-4 border-b border-white/10">
            <div className="flex items-center justify-between">
              <h3 className="text-white font-semibold text-lg flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-purple-400" />
                选择灵感对话
              </h3>
              <button
                onClick={() => setShowInspirationModal(false)}
                className="p-2 hover:bg-white/10 rounded-full transition-all"
              >
                <X className="w-5 h-5 text-white/60" />
              </button>
            </div>
            <p className="text-white/50 text-sm mt-1">
              选择要展示给听众的创作对话（建议3-5条）
            </p>
          </div>
          
          {/* 快速筛选 */}
          <div className="px-6 py-3 border-b border-white/10">
            <div className="flex gap-2">
              <button
                onClick={() => {
                  const allIds = inspirationMessages.map(msg => msg.id);
                  setSelectedDialogues(allIds);
                }}
                className="px-3 py-1.5 bg-white/10 hover:bg-white/15 rounded-full text-white/70 text-xs transition-all"
              >
                全选
              </button>
              <button
                onClick={() => setSelectedDialogues([])}
                className="px-3 py-1.5 bg-white/10 hover:bg-white/15 rounded-full text-white/70 text-xs transition-all"
              >
                清空
              </button>
              <button
                onClick={() => {
                  const highlightedIds = inspirationMessages
                    .filter(msg => msg.highlighted)
                    .map(msg => msg.id);
                  setSelectedDialogues(highlightedIds);
                }}
                className="px-3 py-1.5 bg-gradient-to-r from-purple-500/20 to-pink-500/20 hover:from-purple-500/30 hover:to-pink-500/30 rounded-full text-purple-400 text-xs transition-all"
              >
                AI推荐
              </button>
            </div>
          </div>
          
          {/* 对话列表 */}
          <div className="px-6 py-4 overflow-y-auto" style={{ maxHeight: 'calc(85vh - 280px)' }}>
            <div className="space-y-3">
              {inspirationMessages.map((message, index) => (
                <div
                  key={message.id}
                  onClick={() => {
                    setSelectedDialogues(prev => 
                      prev.includes(message.id)
                        ? prev.filter(id => id !== message.id)
                        : [...prev, message.id]
                    );
                  }}
                  className={`relative p-4 rounded-xl border-2 transition-all cursor-pointer ${
                    selectedDialogues.includes(message.id)
                      ? 'bg-gradient-to-r from-purple-500/10 to-pink-500/10 border-purple-500/50'
                      : 'bg-white/5 border-white/10 hover:bg-white/10'
                  }`}
                >
                  {/* 选中标记 */}
                  {selectedDialogues.includes(message.id) && (
                    <div className="absolute top-3 right-3 w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                      <Check className="w-3 h-3 text-white" />
                    </div>
                  )}
                  
                  {/* AI推荐标记 */}
                  {message.highlighted && (
                    <div className="absolute top-3 left-3 px-2 py-0.5 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full">
                      <span className="text-[10px] text-purple-400 font-medium">AI推荐</span>
                    </div>
                  )}
                  
                  {/* 对话内容 */}
                  <div className="flex gap-3 mt-2">
                    <div className={`w-8 h-8 rounded-full flex-shrink-0 flex items-center justify-center ${
                      message.type === 'user' 
                        ? 'bg-gradient-to-br from-blue-400 to-cyan-400' 
                        : 'bg-gradient-to-br from-purple-400 to-pink-400'
                    }`}>
                      {message.type === 'user' ? (
                        <span className="text-white text-xs font-bold">我</span>
                      ) : (
                        <Sparkles className="w-4 h-4 text-white" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm text-white/70 font-medium">
                          {message.type === 'user' ? '创作者' : '小弦AI'}
                        </span>
                        <span className="text-xs text-white/40">
                          {new Date(message.timestamp).toLocaleTimeString('zh-CN', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </span>
                      </div>
                      <p className="text-sm text-white/80 leading-relaxed">
                        {message.content}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* 底部操作栏 */}
          <div className="px-6 py-4 border-t border-white/10 bg-gray-900/50 backdrop-blur">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm text-white/60">
                已选择 {selectedDialogues.length} 条对话
              </span>
              {selectedDialogues.length > 0 && selectedDialogues.length < 3 && (
                <span className="text-xs text-amber-400 flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  建议至少选择3条
                </span>
              )}
            </div>
            <button
              onClick={() => {
                setShowInspirationModal(false);
                if (selectedDialogues.length > 0) {
                  setAiGuide({
                    message: `完美！已选择${selectedDialogues.length}条精彩对话，这会让你的作品更有故事感 🎵`,
                    mood: 'celebration'
                  });
                }
              }}
              className="w-full py-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-white font-medium hover:scale-105 transition-transform"
            >
              确认选择
            </button>
          </div>
        </div>
      </div>
    )
  );

  // ==================== 主渲染 ====================
  return (
    <div className="relative mx-auto" style={{ maxWidth: '430px', height: '932px' }}>
      {/* 手机框架 */}
      <div className="relative w-full h-full bg-black rounded-[3rem] p-[3px] shadow-2xl">
        <div className="relative w-full h-full bg-gray-900 rounded-[2.8rem] overflow-hidden flex flex-col">
          
          {/* 状态栏 */}
          <div className="absolute top-0 left-0 right-0 z-50 px-8 pt-3 pb-1">
            <div className="flex justify-between items-center text-white text-sm">
              <div className="flex items-center gap-1">
                <span className="font-medium">9:41</span>
              </div>
              <div className="absolute left-1/2 transform -translate-x-1/2 w-24 h-7 bg-black rounded-full" />
              <div className="flex items-center gap-1">
                <div className="w-4 h-4 border border-white/50 rounded-sm">
                  <div className="h-full bg-white rounded-sm" style={{ width: '70%' }} />
                </div>
              </div>
            </div>
          </div>

          {/* 动态背景 */}
          <div className={`absolute inset-0 bg-gradient-to-br ${moodThemes[currentMood].gradient} opacity-10 transition-all duration-1000`} />

          {/* AI灵感丝带 - 固定在顶部 */}
          <div className="px-6 pt-14 pb-3 z-20">
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
              {/* AI头像和状态 */}
              <div className="flex items-start gap-3 mb-3">
                <div className={`w-10 h-10 rounded-full bg-gradient-to-br ${moodThemes[currentMood].gradient} flex items-center justify-center flex-shrink-0`}>
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-white font-medium text-sm">小弦</span>
                    <span className="text-xs text-white/40">AI音乐发布助手</span>
                  </div>
                  <p className="text-white/80 text-sm leading-relaxed">
                    {aiMessage}
                    {aiMessage.length < aiGuide.message.length && (
                      <span className="inline-block w-1 h-4 ml-1 bg-white/60 animate-blink" />
                    )}
                  </p>
                </div>
              </div>
              
              {/* 进度展示 */}
              {aiGuide.progress !== undefined && (
                <div className="flex items-center gap-3">
                  <div className="flex-1 h-1.5 bg-white/10 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-gradient-to-r from-purple-400 to-pink-400 transition-all duration-500 rounded-full"
                      style={{ width: `${aiGuide.progress}%` }}
                    />
                  </div>
                  <span className="text-xs text-white/60 font-medium">{aiGuide.progress}%</span>
                </div>
              )}
              
              {/* 提示文字 */}
              <div className="mt-3 flex items-center gap-1 text-white/40 text-xs">
                <span>💡</span>
                <span>填写越完整，获得的曝光越多哦</span>
              </div>
            </div>
          </div>

          {/* 主内容区域 */}
          <div className="flex-1 overflow-y-auto no-scrollbar">
            {currentStep === 'edit' && renderEditPage()}
            {currentStep === 'publishing' && renderPublishingPage()}
            {currentStep === 'success' && renderSuccessPage()}
          </div>

          {/* 底部固定操作栏 - 优化布局 */}
          {(currentStep === 'edit' || currentStep === 'success') && (
            <div className="absolute bottom-0 left-0 right-0 z-40 px-6 pb-8 pt-4 bg-gradient-to-t from-gray-900 via-gray-900/95 to-transparent">
              <div className="flex items-center gap-3">
                {/* 返回按钮 */}
                <button
                  onClick={handleBack}
                  className="w-14 h-14 bg-white/10 hover:bg-white/15 backdrop-blur-lg rounded-full flex items-center justify-center transition-all hover:scale-105 active:scale-95"
                >
                  <ArrowLeft className="w-6 h-6 text-white" />
                </button>
                
                {/* 发布/继续创作按钮 */}
                <button
                  onClick={currentStep === 'edit' ? handlePublish : () => setCurrentStep('edit')}
                  className={`flex-1 h-14 rounded-full font-medium text-white transition-all transform hover:scale-105 active:scale-95 ${
                    currentStep === 'edit' && completionProgress >= 50
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 shadow-lg shadow-purple-500/30'
                      : currentStep === 'success'
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 shadow-lg shadow-purple-500/30'
                      : 'bg-gray-700 cursor-not-allowed'
                  }`}
                >
                  <span className="flex items-center justify-center gap-2">
                    <Sparkles className="w-5 h-5" />
                    {currentStep === 'edit' ? '立即发布' : '继续创作'}
                  </span>
                </button>
                
                {/* 音律珠 - AI助手 */}
                <button
                  onClick={handleAiChat}
                  className="relative group"
                >
                  {/* 呼吸光环 */}
                  <div className="absolute -inset-1 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 animate-pulse-slow" />
                  
                  {/* 音律珠主体 */}
                  <div className="relative w-14 h-14 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 via-pink-500 to-orange-500 shadow-2xl transform transition-all hover:scale-110">
                    {/* 内部流动效果 */}
                    <div className="absolute inset-0">
                      {[...Array(3)].map((_, i) => (
                        <div
                          key={i}
                          className="absolute w-full h-full animate-flow"
                          style={{
                            background: `radial-gradient(circle at ${30 + i * 20}% ${30 + i * 20}%, rgba(255,255,255,0.3) 0%, transparent 50%)`,
                            animationDelay: `${i * 0.5}s`
                          }}
                        />
                      ))}
                    </div>
                    
                    {/* 中心符号 */}
                    <div className="relative w-full h-full flex items-center justify-center">
                      <Sparkles className="w-6 h-6 text-white" />
                      {/* 状态指示点 */}
                      <div className="absolute bottom-1.5 right-1.5 w-2 h-2 rounded-full bg-white animate-pulse" />
                    </div>
                  </div>
                  
                  {/* 提示文字 */}
                  <div className="absolute -top-8 right-0 bg-black/80 text-white text-xs px-2 py-1 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    AI助手
                  </div>
                </button>
              </div>
            </div>
          )}
          
          {/* 灵感对话选择模态框 */}
          {renderInspirationModal()}

          {/* Home Indicator */}
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white/30 rounded-full" />
        </div>
      </div>
    </div>
  );
};

// ==================== 样式注入 ====================
const style = document.createElement('style');
style.textContent = `
  @keyframes wave {
    0%, 100% { transform: scaleY(0.5); }
    50% { transform: scaleY(1); }
  }
  
  @keyframes flow {
    0% { transform: translateY(100%) rotate(0deg); }
    100% { transform: translateY(-100%) rotate(360deg); }
  }
  
  @keyframes pulse-slow {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.1); }
  }
  
  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }
  
  @keyframes scale-up {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  .animate-wave {
    animation: wave 1.5s ease-in-out infinite;
  }
  
  .animate-flow {
    animation: flow 6s linear infinite;
  }
  
  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }
  
  .animate-blink {
    animation: blink 1s infinite;
  }
  
  .animate-scale-up {
    animation: scale-up 0.3s ease-out forwards;
  }
  
  .animate-spin {
    animation: spin 1s linear infinite;
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`;
document.head.appendChild(style);

export default PublishWork;