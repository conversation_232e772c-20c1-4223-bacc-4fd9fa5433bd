import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { ArrowLeft, Edit3, Music, Sparkles } from 'lucide-react';
import { Link } from 'react-router-dom';

const RemixCreationDesign: React.FC = () => {
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const loadMarkdownContent = async () => {
      try {
        setLoading(true);
        // 动态导入markdown文件
        const response = await fetch(`${import.meta.env.VITE_PUBLIC_BASE_PATH}docs/RemixCreationDesign.md`);
        if (!response.ok) {
          throw new Error('Failed to load RemixCreationDesign document');
        }
        const text = await response.text();
        setContent(text);
      } catch (err) {
        setError('加载二创页面设计文档失败，请稍后重试');
        console.error('Error loading markdown:', err);
      } finally {
        setLoading(false);
      }
    };

    loadMarkdownContent();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm shadow-sm border-b border-purple-100">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center">
            <Link 
              to="/" 
              className="flex items-center text-gray-600 hover:text-purple-600 transition-colors mr-4"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              返回首页
            </Link>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <Edit3 className="w-4 h-4 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                二创页面功能设计
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-purple-100 p-8">
          <div className="prose prose-lg max-w-none">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                <span className="ml-3 text-gray-600">加载中...</span>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <p className="text-red-600 mb-4">{error}</p>
                <button 
                  onClick={() => window.location.reload()} 
                  className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
                >
                  重新加载
                </button>
              </div>
            ) : (
              <ReactMarkdown 
                remarkPlugins={[remarkGfm]}
                components={{
                  h1: ({children}) => (
                    <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-6 pb-3 border-b border-purple-200">
                      {children}
                    </h1>
                  ),
                  h2: ({children}) => (
                    <h2 className="text-2xl font-semibold text-gray-800 mt-8 mb-4 flex items-center gap-2">
                      <Sparkles className="w-6 h-6 text-purple-500" />
                      {children}
                    </h2>
                  ),
                  h3: ({children}) => (
                    <h3 className="text-xl font-semibold text-purple-700 mt-6 mb-3 flex items-center gap-2">
                      <Music className="w-5 h-5 text-pink-500" />
                      {children}
                    </h3>
                  ),
                  h4: ({children}) => <h4 className="text-lg font-semibold text-gray-700 mt-4 mb-2">{children}</h4>,
                  p: ({children}) => <p className="text-gray-600 leading-relaxed mb-4">{children}</p>,
                  ul: ({children}) => <ul className="list-disc list-inside text-gray-600 mb-4 space-y-2">{children}</ul>,
                  ol: ({children}) => <ol className="list-decimal list-inside text-gray-600 mb-4 space-y-2">{children}</ol>,
                  li: ({children}) => <li className="ml-4 leading-relaxed">{children}</li>,
                  strong: ({children}) => <strong className="font-semibold text-purple-700">{children}</strong>,
                  code: ({children}) => (
                    <code className="bg-purple-100 px-2 py-1 rounded text-sm font-mono text-purple-800">
                      {children}
                    </code>
                  ),
                  blockquote: ({children}) => (
                    <blockquote className="border-l-4 border-purple-500 pl-4 italic text-gray-600 my-4 bg-purple-50 py-2 rounded-r">
                      {children}
                    </blockquote>
                  ),
                  table: ({children}) => (
                    <div className="overflow-x-auto my-6">
                      <table className="min-w-full border border-purple-200 rounded-lg overflow-hidden">
                        {children}
                      </table>
                    </div>
                  ),
                  thead: ({children}) => (
                    <thead className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                      {children}
                    </thead>
                  ),
                  th: ({children}) => (
                    <th className="px-4 py-3 text-left font-semibold">
                      {children}
                    </th>
                  ),
                  td: ({children}) => (
                    <td className="px-4 py-3 border-t border-purple-100">
                      {children}
                    </td>
                  )
                }}
              >
                {content}
              </ReactMarkdown>
            )}
          </div>
        </div>
        
        {/* Feature Highlights */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-purple-100 hover:shadow-lg transition-all">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-4">
              <Edit3 className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">智能二创引导</h3>
            <p className="text-gray-600 text-sm">AI小弦提供个性化的二创建议和技术支持，让每个用户都能轻松开始创作。</p>
          </div>
          
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-purple-100 hover:shadow-lg transition-all">
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mb-4">
              <span className="text-white font-bold text-lg">💰</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">透明收益分配</h3>
            <p className="text-gray-600 text-sm">基于AI评估的智能分成机制，保障原作者和二创者的合理收益。</p>
          </div>
          
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-purple-100 hover:shadow-lg transition-all">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mb-4">
              <span className="text-white font-bold text-lg">🎵</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">多维度创作工具</h3>
            <p className="text-gray-600 text-sm">从旋律改编到声音克隆，提供全方位的音乐创作和编辑功能。</p>
          </div>
        </div>
        
        {/* Edit Notice */}
        <div className="mt-8 p-6 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-xl">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
              <Sparkles className="w-4 h-4 text-white" />
            </div>
            <div>
              <p className="text-purple-800 text-sm mb-2">
                <strong>💡 设计说明：</strong> 这个二创页面设计文档来自 <code>/public/docs/RemixCreationDesign.md</code> 文件。
              </p>
              <p className="text-purple-700 text-sm">
                该设计深度思考了用户二创动机、原作者收益保障、以及完整的创作生态构建，
                旨在打造一个健康可持续的音乐创作社区。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RemixCreationDesign;