// vite.config.ts
import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 根据当前是 'development' 还是 'production' 模式加载对应的 .env 文件
  const env = loadEnv(mode, process.cwd(), '');

  return {
    plugins: [react()],
    // 从环境变量 VITE_PUBLIC_BASE_PATH 读取 base
    // 如果这个变量不存在，就默认使用根路径 '/'
    base: env.VITE_PUBLIC_BASE_PATH || '/',
  }
})