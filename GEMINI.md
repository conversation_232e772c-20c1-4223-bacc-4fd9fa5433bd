# GEMINI Project Analysis: AI Music Community

## 1. Project Overview

This project, named **"心弦 MoodBeat"**, is a web application for an AI-powered music creation and sharing community. The core concept is to enable users, particularly amateurs and hobbyists, to create songs from their stories and emotions using AI assistance.

The application is a modern single-page application (SPA) built with **React**, **Vite**, and **TypeScript**. It uses **Tailwind CSS** for styling and `react-router-dom` for client-side routing.

A key feature of this repository is the extensive product and design documentation located in the `public/docs/` directory. These Markdown files (like `PRD.md`, `HomeDesign.md`, etc.) provide a detailed blueprint for the project's vision, features, and user experience. The application itself renders these documents on specific routes, making it a living documentation platform as well as the foundation for the final product.

## 2. Building and Running

The project uses `npm` as the package manager. The primary commands are defined in `package.json`.

### Installation

To install all the necessary dependencies, run:
```bash
npm install
```

### Development

To start the local development server with Hot Module Replacement (HMR):
```bash
npm run dev
```
The server will typically be available at `http://localhost:5173`.

### Building for Production

To compile and minify the application for production, run:
```bash
npm run build
```
The output will be placed in the `dist/` directory.

### Linting

To check the code for style and quality issues using ESLint, run:
```bash
npm run lint
```

## 3. Development Conventions

*   **Technology Stack**: The project is standardized on React, Vite, and TypeScript. All new components and logic should be written in TypeScript (`.tsx`).
*   **Component-Based Architecture**: The application is structured around components. Pages are located in `src/pages/`, and reusable components should be placed in `src/components/`.
*   **Routing**: All application routes are centrally managed in `src/App.tsx`. When adding a new page, you must add a new `<Route>` entry here.
*   **Documentation-Driven**: The detailed Markdown files in `public/docs/` are the source of truth for the application's features and design. Many of the existing pages in `src/pages` are dedicated to rendering this documentation. This suggests a development workflow where design and requirements are first specified in Markdown.
*   **Styling**: Styling is handled by Tailwind CSS. Utility-first classes should be used for styling components directly in the JSX.
*   **Environment Variables**: The Vite configuration in `vite.config.ts` uses environment variables (e.g., `VITE_PUBLIC_BASE_PATH` from a `.env` file) to configure the application's base path, which is important for deployment.
