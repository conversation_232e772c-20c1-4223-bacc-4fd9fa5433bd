import React from 'react';
import React<PERSON>low, { MiniMap, Controls, Background } from 'reactflow';
import type { Node, Edge } from 'reactflow';
import 'reactflow/dist/style.css';
import { 
  Music, Home, Brain, Upload, Map, Heart, Sparkles, 
  Play, Coffee, Cake, HeartCrack, Moon, Star, Users,
  MessageCircle, Share2, Volume2, Mic, Camera, Hash,
  ArrowLeft
} from 'lucide-react';

// 导入真实页面组件
import MusicHome from './MusicHome';
import AIMusicCreation from './AIMusicCreation';
import AIMusicAppreciation from './AIMusicAppreciation';
import PublishWork from './PublishWork';

// PagePreview 容器组件 - 针对iPhone 16 Pro Max优化
const PagePreview: React.FC<{
  children: React.ReactNode;
  scale?: number;
  width?: number;
  height?: number;
}> = ({ children, scale = 0.15, width = 430, height = 932 }) => {
  return (
    <div 
      className="preview-container overflow-hidden bg-white rounded-lg shadow-sm"
      style={{
        width: `${width * scale}px`,
        height: `${height * scale}px`
      }}
    >
      <div 
        style={{
          width: `${width}px`,
          height: `${height}px`,
          transform: `scale(${scale})`,
          transformOrigin: 'top left'
        }}
      >
        {children}
      </div>
    </div>
  );
};

// 定义所有APP页面节点 - iPhone 16 Pro Max尺寸
const initialNodes: Node[] = [
  // 音乐首页
  { 
    id: 'music-home', 
    data: { 
      label: (
        <PagePreview scale={0.15} width={430} height={932}>
          <MusicHome />
        </PagePreview>
      )
    }, 
    position: { x: 400, y: 50 },
    style: {
      background: 'transparent',
      border: 'none',
      borderRadius: '12px',
      padding: '0',
      width: '65px',
      height: '140px'
    }
  },
  // AI音乐创建
  { 
    id: 'ai-music-creation', 
    data: { 
      label: (
        <PagePreview scale={0.15} width={430} height={932}>
          <AIMusicCreation />
        </PagePreview>
      )
    }, 
    position: { x: 550, y: 200 },
    style: {
      background: 'transparent',
      border: 'none',
      borderRadius: '12px',
      padding: '0',
      width: '65px',
      height: '140px'
    }
  },
  // AI音乐鉴赏
  { 
    id: 'ai-music-appreciation', 
    data: { 
      label: (
        <PagePreview scale={0.15} width={430} height={932}>
          <AIMusicAppreciation />
        </PagePreview>
      )
    }, 
    position: { x: 250, y: 200 },
    style: {
      background: 'transparent',
      border: 'none',
      borderRadius: '12px',
      padding: '0',
      width: '65px',
      height: '140px'
    }
  },
  // 发布作品
  { 
    id: 'publish-work', 
    data: { 
      label: (
        <PagePreview scale={0.15} width={430} height={932}>
          <PublishWork />
        </PagePreview>
      )
    }, 
    position: { x: 400, y: 350 },
    style: {
      background: 'transparent',
      border: 'none',
      borderRadius: '12px',
      padding: '0',
      width: '65px',
      height: '140px'
    }
  }
];

// 定义节点间的连接关系
const initialEdges: Edge[] = [
  // 音乐首页连接到AI音乐创建和AI音乐鉴赏
  { id: 'music-home-ai-creation', source: 'music-home', target: 'ai-music-creation', type: 'smoothstep', animated: true, style: { stroke: '#2563eb', strokeWidth: 2, strokeDasharray: '5,5' } },
  { id: 'music-home-ai-appreciation', source: 'music-home', target: 'ai-music-appreciation', type: 'smoothstep', animated: true, style: { stroke: '#7c3aed', strokeWidth: 2, strokeDasharray: '5,5' } },
  
  // AI音乐创建连接到发布作品
  { id: 'ai-creation-publish', source: 'ai-music-creation', target: 'publish-work', type: 'smoothstep', animated: true, style: { stroke: '#06b6d4', strokeWidth: 2, strokeDasharray: '3,3' } }
];

const SiteMap = () => {
  const handleGoBack = () => {
    // 使用浏览器的历史记录返回功能
    window.history.back();
  };

  React.useEffect(() => {
    // 添加CSS样式
    const style = document.createElement('style');
    style.textContent = `
      /* 预览容器样式 - 禁用动画和交互 */
      .preview-container * {
        animation-duration: 0s !important;
        animation-delay: 0s !important;
        transition-duration: 0s !important;
        pointer-events: none !important;
        user-select: none !important;
      }
      
      /* 隐藏滚动条 */
      .preview-container ::-webkit-scrollbar {
        display: none !important;
      }
      
      .preview-container {
        -ms-overflow-style: none !important;
        scrollbar-width: none !important;
      }
      
      /* 确保预览容器内容不会溢出 */
      .preview-container * {
        overflow: hidden !important;
      }
      
      /* 禁用所有hover效果 */
      .preview-container *:hover {
        transform: none !important;
        background: inherit !important;
        color: inherit !important;
      }

      /* 优化高缩放下的控件显示 */
      .react-flow__controls {
        position: fixed !important;
        bottom: 20px !important;
        left: 20px !important;
        z-index: 1000 !important;
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px) !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
      }

      .react-flow__minimap {
        position: fixed !important;
        bottom: 20px !important;
        right: 20px !important;
        z-index: 1000 !important;
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px) !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
      }

      /* 确保在高缩放下预览内容仍然清晰 */
      .preview-container {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
      }
      
      @keyframes wave {
        0%, 100% { height: 8px; }
        50% { height: 16px; }
      }
      
      @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-4px); }
      }
      
      @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
      }
      
      .animate-wave {
        animation: wave 1.5s ease-in-out infinite;
      }
      
      .animate-float {
        animation: float 3s ease-in-out infinite;
      }
      
      .shimmer {
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        background-size: 200% 100%;
        animation: shimmer 2s infinite;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);
  
  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 优化后的页面标题栏 - 更紧凑的设计 */}
      <div style={{ 
        padding: '10px 20px', 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        minHeight: '60px'
      }}>
        {/* 左侧返回按钮 */}
        <button
          onClick={handleGoBack}
          style={{
            background: 'rgba(255, 255, 255, 0.2)',
            border: 'none',
            borderRadius: '8px',
            color: 'white',
            padding: '8px 12px',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            cursor: 'pointer',
            fontSize: '14px',
            transition: 'all 0.2s ease',
            backdropFilter: 'blur(10px)'
          }}
          onMouseEnter={(e) => {
            const target = e.target as HTMLElement;
            target.style.background = 'rgba(255, 255, 255, 0.3)';
            target.style.transform = 'translateX(-2px)';
          }}
          onMouseLeave={(e) => {
            const target = e.target as HTMLElement;
            target.style.background = 'rgba(255, 255, 255, 0.2)';
            target.style.transform = 'translateX(0)';
          }}
        >
          <ArrowLeft size={16} />
          返回
        </button>

        {/* 中间标题区域 */}
        <div style={{ textAlign: 'center', flex: 1 }}>
          <h1 style={{ margin: '0 0 4px 0', fontSize: '18px', fontWeight: 'bold' }}>
            🗺️ APP页面地图
          </h1>
          <p style={{ margin: 0, fontSize: '12px', opacity: 0.9 }}>
            实时预览版 · iPhone 16 Pro Max尺寸 · 可自由缩放查看
          </p>
        </div>

        {/* 右侧占位，保持布局平衡 */}
        <div style={{ width: '76px' }}></div>
      </div>
      
      {/* ReactFlow 图表区域 - 优化缩放和布局稳定性 */}
      <div style={{ flex: 1, position: 'relative', overflow: 'hidden' }}>
        <ReactFlow 
          nodes={initialNodes} 
          edges={initialEdges}
          fitView
          fitViewOptions={{ 
            padding: 0.1,
            includeHiddenNodes: true,
            minZoom: 0.1,
            maxZoom: 8
          }}
          nodesDraggable={true}
          nodesConnectable={false}
          elementsSelectable={true}
          minZoom={0.1}
          maxZoom={8}
          zoomOnScroll={true}
          zoomOnPinch={true}
          panOnScroll={false}
          panOnScrollMode={"free" as any}
          style={{ 
            background: '#fafbfc',
            width: '100%',
            height: '100%'
          }}
        >
          <MiniMap 
            style={{
              height: 120,
              width: 200,
              backgroundColor: 'rgba(248, 250, 252, 0.95)',
              backdropFilter: 'blur(10px)'
            }}
            zoomable
            pannable
            nodeColor={(node) => '#e2e8f0'}
            maskColor="rgba(0, 0, 0, 0.1)"
          />
          <Controls 
            style={{
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)'
            }}
            showZoom={true}
            showFitView={true}
            showInteractive={false}
          />
          <Background 
            gap={20} 
            size={1}
            color="#e2e8f0"
            variant={"dots" as any}
          />
        </ReactFlow>
      </div>
    </div>
  );
};

export default SiteMap;