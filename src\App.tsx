import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Index from './pages/Index';
import MusicHome from './pages/MusicHome';
import AIMusicCreation from './pages/AIMusicCreation';
import AIMusicAppreciation from './pages/AIMusicAppreciation';
import PublishWork from './pages/PublishWork';
import PRD from './pages/PRD';
import Research from './pages/Research';
import HomeDesign from './pages/HomeDesign';
import MusicCreationDesign from './pages/MusicCreationDesign';
import PublishPageDesign from './pages/PublishPageDesign';
import AIMusicAppreciationDesign from './pages/AIMusicAppreciationDesign';
import RemixCreationDesign from './pages/RemixCreationDesign';
import RemixCreationPage from './pages/RemixCreationPage';
import Template from './pages/template';
import SiteMap from './pages/SiteMap';

function App() {
  return (
    <Router basename={import.meta.env.VITE_PUBLIC_BASE_PATH}>
      <div className="App">
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/music-home" element={<MusicHome />} />
          <Route path="/ai-music-creation" element={<AIMusicCreation />} />
          <Route path="/ai-music-appreciation" element={<AIMusicAppreciation />} />
          <Route path="/publish-work" element={<PublishWork />} />
          <Route path="/prd" element={<PRD />} />
          <Route path="/research" element={<Research />} />
          <Route path="/home-design" element={<HomeDesign />} />
          <Route path="/music-creation-design" element={<MusicCreationDesign />} />
          <Route path="/publish-page-design" element={<PublishPageDesign />} />
          <Route path="/ai-music-appreciation-design" element={<AIMusicAppreciationDesign />} />
          <Route path="/remix-creation-design" element={<RemixCreationDesign />} />
          <Route path="/remix-creation" element={<RemixCreationPage />} />
          <Route path="/template" element={<Template />} />
          <Route path="/site-map" element={<SiteMap />} />
          {/* 预留其他路由 */}

        </Routes>
      </div>
    </Router>
  );
}

export default App
